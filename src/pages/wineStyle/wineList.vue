<template>
    <div>
        <el-card shadow="hover">
            <!-- 高级查询 -->
            <el-input
                class="w-large m-r-10"
                size="mini"
                v-model="queryWineData.keyword"
                @keyup.enter.native="search"
                placeholder=" 输入ID或酒款名称"
            />
            <el-select
                v-model="queryWineData.edit_time_sort"
                placeholder="请选择更新时间排序"
                size="mini"
                clearable
                @change="search"
                class="w-large m-r-10"
            >
                <el-option label="倒序" :value="1"> </el-option>
                <el-option label="正序" :value="0"> </el-option>
            </el-select>

            <el-button type="warning" @click="search" size="mini"
                >查询</el-button
            >
            <el-button type="success" @click="addWine" size="mini"
                >添加</el-button
            >
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                border
                :data="DataList"
                highlight-current-row
                style="width: 100%"
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column label="ID" prop="id"></el-table-column>
                <el-table-column label="图片" prop="moddle_image" width="100">
                    <template slot-scope="scope">
                        <el-image
                            style="width: 80px; height: 80px"
                            :src="scope.row.master_image"
                            fit="fit"
                        >
                            <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i></div
                        ></el-image>
                        <!-- <el-image>
                <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                </div>
                </el-image> -->
                    </template>
                </el-table-column>
                <el-table-column
                    label="中文名"
                    prop="winename"
                    width="200"
                ></el-table-column>
                <el-table-column
                    label="英文名"
                    prop="wename"
                    width="200"
                ></el-table-column>
                <el-table-column label="国家" width="120">
                    <template slot-scope="scope">
                        {{ scope.row.country_name }}
                    </template>
                </el-table-column>
                <el-table-column label="酒庄" width="120">
                    <template slot-scope="scope">
                        {{ scope.row.chateau_name }}
                    </template>
                </el-table-column>
                <el-table-column label="品种类型" prop="id">
                    <template slot-scope="scope">
                        {{ wineVision[scope.row.colormid] }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="年份"
                    prop="wyear"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="适饮期"
                    prop="readyd_rink"
                    width="120"
                ></el-table-column>
                <el-table-column label="参考价" width="150">
                    <template slot-scope="scope">
                        {{ scope.row.minprice }} - {{ scope.row.maxprice }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="上传者"
                    prop="adname"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="上传时间"
                    prop="add_time"
                    width="180"
                ></el-table-column>
                <el-table-column label="操作" fixed="right" width="150">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="editWine(scope.row.id)"
                            >编辑</el-button
                        >
                        <el-button
                            type="text"
                            size="mini"
                            @click="rateWine(scope.row)"
                            >评分</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div style="display: flex; justify-content: center; margin-top: 20px">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryWineData.limit"
                :current-page="queryWineData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="酒款设置"
            :visible.sync="wineVisible"
            width="80%"
            @close="closeDialog"
            :close-on-click-modal="false"
        >
            <wine
                ref="updateWineRef"
                v-if="wineVisible"
                @updateSuccess="updateSuccess"
            ></wine>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="wineVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmUpdateWine"
                    >确定</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="ratingVisible"
            width="80%"
            @close="closeRatingDialog"
        >
            <wineRating ref="wineRating" v-if="ratingVisible"></wineRating>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button type="primary" @click="ratingVisible = false"
                    >确认</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import wine from "./wineOp.vue";
import wineRating from "./wineRating.vue";
export default {
    components: {
        wine,
        wineRating,
    },
    data() {
        return {
            total: 0, // 总条数
            queryWineData: {
                keyword: "",
                page: 1,
                limit: 10,
                edit_time_sort: "",
            },
            DataList: [],
            wineVisible: false,
            loading: false,
            statusTxt: {
                0: "未开奖",
                1: "蓝方获胜",
                2: "红方获胜",
            },
            statusOption: [
                {
                    label: "启用",
                    value: 1,
                },
                {
                    label: "禁用",
                    value: 2,
                },
            ],
            wineVision: {},
            ratingVisible: false,
        };
    },
    created() {
        this.getData();
        var _this = this;
        document.onkeydown = function (e) {
            var key = window.event.keyCode;
            if (key == 13) {
                _this.getData();
            }
        };
    },
    mounted() {
        this.getAscategorylist();
    },
    methods: {
        getAscategorylist() {
            this.$request.winestyle.getAscategorylist().then((res) => {
                if (res.data.error_code == 0) {
                    res.data.data.asc.vision.map((item) => {
                        this.wineVision[item.id] = item.name;
                    });
                }
            });
        },
        closeDialog() {
            this.getData();
        },
        updateSuccess() {
            this.wineVisible = false;
        },

        handleSelectionChange() {},
        editWine(id) {
            this.wineVisible = true;
            this.$nextTick(() => {
                this.$refs.updateWineRef.editWine(id);
            });
        },
        addWine(row) {
            this.wineVisible = true;
            this.$nextTick(() => {
                this.$refs.updateWineRef.addWine();
            });
        },
        comfirmUpdateWine() {
            this.wineVisible = true;
            this.$refs.updateWineRef.submit();
        },
        getData() {
            this.$request.winestyle
                .getWineList(this.queryWineData)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.DataList = res.data.data.list || [];
                        this.total = res.data.data.total;
                    }
                });
        },
        queryStatus() {
            this.queryWineData.page = 1;
            this.getData();
        },
        // 高级查询
        search() {
            this.queryWineData.page = 1;
            this.getData();
        },
        // 改变每页条数
        handleSizeChange(val) {
            this.queryWineData.limit = val;
            this.getData();
        },
        // 改变当前页
        handleCurrentChange(val) {
            this.queryWineData.page = val;
            this.getData();
        },
        rateWine(row) {
            this.ratingVisible = true;
            this.$nextTick(() => {
                this.$refs.wineRating.getWineRatingList(row);
            });
        },
        closeRatingDialog() {},
    },
};
</script>
<style lang="scss" scoped>
.el-image {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    font-size: 20px;
    .image-slot {
    }
}
</style>

<template>
    <div>
        <el-card shadow="hover">
            <el-form :model="skepticalWineQueryData" :inline="true" size="mini">
                <el-form-item>
                    <el-input
                        clearable
                        v-model="skepticalWineQueryData.keyword"
                        placeholder="请输入中英文酒款名"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="skepticalWineQueryData.status"
                        placeholder="请选择状态"
                        clearable
                        @change="querySkepticalWine"
                    >
                        <el-option
                            v-for="item in statusOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        clearable
                        v-model="skepticalWineQueryData.uid"
                        placeholder="请输入用户ID"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        clearable
                        v-model="skepticalWineQueryData.uname"
                        placeholder="请输入用户昵称"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="querySkepticalWine"
                        >查询</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top: 20px">
            <el-table
                :data="skepticalWineList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="ID" prop="id" width="100">
                </el-table-column>
                <el-table-column label="用户ID" prop="uid" width="110">
                </el-table-column>
                <el-table-column label="用户昵称" prop="uname" width="120">
                </el-table-column>
                <el-table-column label="中文名">
                    <template slot-scope="{ row }">
                        <div>
                            {{ row.winename || "-" }}
                        </div>
                        <div>原中文名:{{ row.old_winename || "-" }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="英文名">
                    <template slot-scope="{ row }">
                        <div>
                            {{ row.wename || "-" }}
                        </div>
                        <div>原英文名:{{ row.old_wename || "-" }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="提交时间" prop="add_time" width="160">
                </el-table-column>
                <el-table-column label="状态" prop="status" width="120">
                    <template slot-scope="{ row }">
                        {{ statusParams[row.status] }}
                    </template>
                </el-table-column>
                <el-table-column label="处理人" prop="adname" width="120">
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="100">
                    <template slot-scope="{ row }">
                        <el-button
                            v-if="row.status === 1"
                            type="text"
                            size="mini"
                            @click="handleWine(row)"
                            >处理</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="skepticalWineQueryData.limit"
                :current-page="skepticalWineQueryData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            :visible.sync="handleVisible"
            @close=""
            width="80%"
            :close-on-click-modal="false"
        >
            <handleSkepticalWine
                ref="skepticalWineDetailRef"
                v-if="handleVisible"
                @handleSuccess="handleSuccess"
            >
            </handleSkepticalWine>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="rejectWine">拒绝发放兔头</el-button>
                <el-button type="primary" @click="confirmSubmitWine"
                    >同意发放兔头</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import handleSkepticalWine from "./handleSkepticalWine.vue";
export default {
    name: "Vue2ProductLibrarySkepticalWine",
    components: { handleSkepticalWine },
    data() {
        return {
            skepticalWineQueryData: {
                uid: "",
                uname: "",
                winename: "",
                status: "",
                keyword: "",
                limit: 10,
                page: 1,
            },
            total: 0,
            skepticalWineList: [],
            handleVisible: false,
            // 1未处理 2已处理 3已拒绝
            statusOption: [
                {
                    value: 1,
                    label: "未处理",
                },
                {
                    value: 2,
                    label: "已处理",
                },
                {
                    value: 3,
                    label: "已拒绝",
                },
            ],
            statusParams: {
                1: "未处理",
                2: "已处理",
                3: "已拒绝",
            },
        };
    },

    mounted() {
        this.getSkepticalWineList();
    },

    methods: {
        handleSuccess() {
            this.handleVisible = false;
        },
        querySkepticalWine() {
            this.skepticalWineQueryData.page = 1;
            this.getSkepticalWineList();
        },
        confirmSubmitWine() {
            this.$refs.skepticalWineDetailRef.changeWineDetailsStauts();
        },
        rejectWine() {
            this.$refs.skepticalWineDetailRef.rejectWineDetailsStauts();
        },
        getSkepticalWineList() {
            this.$request.winestyle
                .getSkepticalWine(this.skepticalWineQueryData)
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.skepticalWineList = res.data.data.list;
                        this.total = res.data.data.total || 0;
                    }
                });
        },

        handleWine(row) {
            // this.$request.
            this.handleVisible = true;
            this.$nextTick(() => {
                this.$refs.skepticalWineDetailRef.getSkepticalWineDetails(
                    row.id
                );
            });
        },
        handleSizeChange(limit) {
            this.skepticalWineQueryData.limit = limit;
            this.skepticalWineQueryData.page = 1;
            this.getSkepticalWineList();
        },
        handleCurrentChange(page) {
            this.skepticalWineQueryData.page = page;
            this.getSkepticalWineList();
        },
    },
};
</script>

<style lang="scss" scoped></style>

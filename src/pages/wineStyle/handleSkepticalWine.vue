<template>
    <div class="handle_container">
        <el-card shadow="hover" style="width: 49%">
            <!-- <div style="font-size: 16px; font-weight: 600; margin-bottom: 20px">
                修改数据
            </div> -->
            <template slot="header">
                <div style="font-size: 16px; font-weight: 600">修改数据</div>
            </template>
            <el-form
                :model="ecdata"
                label-width="120px"
                :inline="false"
                size="normal"
            >
                <div
                    style="display: flex"
                    :class="
                        SUBMIT_PARAMS.master_image
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div style="display: flex; align-items: center">
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.master_image"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="用户上传图片">
                        <vos-oss
                            list-type="picture-card"
                            :showFileList="true"
                            :limit="8"
                            :file-list="showData.currentfileList"
                            :disabled="true"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </vos-oss>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.wename
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.wename"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="酒款名称">
                        <div :class="CHANGE_PARAMS.wename ? 'red_text' : ''">
                            {{ ecdata.wename || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.country_name
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.country_name"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="国家">
                        <div
                            :class="
                                CHANGE_PARAMS.country_name ? 'red_text' : ''
                            "
                        >
                            {{ ecdata.country_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.chateau_name
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.chateau_name"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="酒庄">
                        <div
                            :class="
                                CHANGE_PARAMS.chateau_name ? 'red_text' : ''
                            "
                        >
                            {{ ecdata.chateau_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.regions_name
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.regions_name"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="产区">
                        <div
                            :class="
                                CHANGE_PARAMS.regions_name ? 'red_text' : ''
                            "
                        >
                            {{ ecdata.regions_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.wyear
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.wyear"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="年份">
                        <div :class="CHANGE_PARAMS.wyear ? 'red_text' : ''">
                            {{ ecdata.wyear || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.grape_name
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.grape_name"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="葡萄品种">
                        <div
                            :class="CHANGE_PARAMS.grape_name ? 'red_text' : ''"
                        >
                            {{ ecdata.grape_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.color_name
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.color_name"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="观色">
                        <div
                            :class="CHANGE_PARAMS.color_name ? 'red_text' : ''"
                        >
                            {{ ecdata.color_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.smell_name
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.smell_name"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="闻香">
                        <div
                            :class="CHANGE_PARAMS.smell_name ? 'red_text' : ''"
                        >
                            {{ ecdata.smell_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.taste_name
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.taste_name"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="品味">
                        <div
                            :class="CHANGE_PARAMS.taste_name ? 'red_text' : ''"
                        >
                            {{ ecdata.taste_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.readyd_rink
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.readyd_rink"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="适饮期">
                        <div
                            :class="CHANGE_PARAMS.readyd_rink ? 'red_text' : ''"
                        >
                            {{ ecdata.readyd_rink || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.food_name
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.food_name"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="配餐">
                        <div :class="CHANGE_PARAMS.food_name ? 'red_text' : ''">
                            {{ ecdata.food_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.temper
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.temper"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="侍酒温度">
                        <div :class="CHANGE_PARAMS.temper ? 'red_text' : ''">
                            {{ ecdata.temper || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    :class="
                        SUBMIT_PARAMS.scores
                            ? 'check_item check_item_base'
                            : 'check_item_base'
                    "
                >
                    <div>
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.scores"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="评分">
                        <div
                            style="white-space: pre-wrap"
                            :class="CHANGE_PARAMS.scores ? 'red_text' : ''"
                        >
                            {{ showData.currentScores || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <!-- <div style="display: flex">
                    <div style="display: flex; align-items: center">
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.wename"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="获奖">
                        {{ ecdata.wename }}
                    </el-form-item>
                </div> -->
            </el-form>
        </el-card>
        <el-card shadow="hover" style="width: 49%">
            <template slot="header">
                <div style="font-size: 16px; font-weight: 600">原数据</div>
            </template>
            <el-form
                :model="ecdata"
                label-width="120px"
                :inline="false"
                size="normal"
            >
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="用户上传图片">
                        <vos-oss
                            list-type="picture-card"
                            :showFileList="true"
                            :limit="8"
                            :file-list="showData.oringinFilelist"
                            :disabled="true"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </vos-oss>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="酒款名称">
                        <div>
                            {{ wcdata.wename || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="国家">
                        <div>
                            {{ wcdata.country_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="酒庄">
                        <div>
                            {{ wcdata.chateau_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="产区">
                        <div>
                            {{ wcdata.regions_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="年份">
                        <div>
                            {{ wcdata.wyear || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="葡萄品种">
                        <div>
                            {{ wcdata.grape_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="观色">
                        <div>
                            {{ wcdata.color_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="闻香">
                        <div>
                            {{ wcdata.smell_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="品味">
                        <div>
                            {{ wcdata.taste_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="适饮期">
                        <div>
                            {{ wcdata.readyd_rink || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="配餐">
                        <div>
                            {{ wcdata.food_name || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="侍酒温度">
                        <div>
                            {{ wcdata.temper || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; align-items: center"
                    class="check_item_base"
                >
                    <el-form-item label="评分">
                        <div style="white-space: pre-wrap">
                            {{ showData.originScores || "-" }}
                        </div>
                    </el-form-item>
                </div>
                <!-- <div style="display: flex">
                    <div style="display: flex; align-items: center">
                        <el-checkbox
                            v-model="SUBMIT_PARAMS.wename"
                        ></el-checkbox>
                    </div>
                    <el-form-item label="获奖">
                        {{ ecdata.wename }}
                    </el-form-item>
                </div> -->
            </el-form>
        </el-card>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    name: "Vue2ProductLibraryHandleSkepticalWine",
    components: {
        VosOss,
    },
    data() {
        return {
            ecdata: {},
            wcdata: {},
            SUBMIT_PARAMS: {
                master_image: false,
                wename: false,
                country_name: false,
                chateau_name: false,
                regions_name: false,
                wyear: false,
                grape_name: false,
                color_name: false,
                smell_name: false,
                taste_name: false,
                readyd_rink: false,
                food_name: false,
                temper: false,
                scores: false,
                // master_image: false,
            },
            EXCUTE_PARAMS: {
                master_image: "master_image",
                wename: "wename",
                country_name: "cid",
                chateau_name: "chaid",
                regions_name: "aid",
                wyear: "wyear",
                grape_name: "gid",
                color_name: "colorid",
                smell_name: "smellid",
                taste_name: "tasteid",
                readyd_rink: "readyd_rink",
                food_name: "foodid",
                temper: "temper",
                scores: "scores",
            },
            CHANGE_PARAMS: {
                master_image: false,
                wename: false,
                country_name: false,
                chateau_name: false,
                regions_name: false,
                wyear: false,
                grape_name: false,
                color_name: false,
                smell_name: false,
                taste_name: false,
                readyd_rink: false,
                food_name: false,
                temper: false,
                scores: false,
            },
            fileList: [],
            id: "",
            total: 0,
            showData: {
                currentfileList: [],
                oringinFilelist: [],
                currentScores: "",
                originScores: "",
            },
            scoresKey: {
                danx: "获奖名",
                score: "分数",
            },
        };
    },

    mounted() {},

    methods: {
        changeWineDetailsStauts() {
            let isChange = false;
            for (const key in this.SUBMIT_PARAMS) {
                if (this.SUBMIT_PARAMS[key]) {
                    isChange = true;
                }
            }
            if (!isChange) {
                this.$message.warning("请选择要更改的数据");
                return;
            }
            this.$prompt("请输入要发放的兔头", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputPattern: /\b[1-9]\d{0,1}\b|\b[1]\d\d\b|\b200\b/,
                inputErrorMessage: "请输入1-200的兔头数量",
            }).then(({ value }) => {
                let data = {
                    id: this.id,
                    rabbit: value,
                    status: 2,
                };
                for (const key in this.SUBMIT_PARAMS) {
                    if (this.SUBMIT_PARAMS[key]) {
                        data[this.EXCUTE_PARAMS[key]] =
                            this.ecdata[this.EXCUTE_PARAMS[key]];
                    }
                }
                this.$request.winestyle
                    .changeWineDetailsStauts(data)
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.$message.success("操作成功");
                            this.$emit("handleSuccess");
                        }
                    });
            });
        },
        rejectWineDetailsStauts() {
            this.$confirm("确认拒绝该条修改吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                let data = {
                    id: this.id,
                    status: 3,
                };
                this.$request.winestyle
                    .changeWineDetailsStauts(data)
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.$message.success("操作成功");
                            this.$emit("handleSuccess");
                        }
                    });
            });
        },
        ArrayToString(arr) {
            if (arr.length > 0) {
                let strArr = [];
                strArr = arr.map((item) => {
                    let strItem = "";
                    for (const key in item) {
                        strItem =
                            (strItem !== "" ? strItem + "," : "") +
                            (this.scoresKey[key] + ":" + item[key]);
                    }
                    return strItem;
                });
                return strArr.join("/n");
            } else {
                return "";
            }
        },
        getSkepticalWineDetails(id) {
            this.id = id;
            this.$request.winestyle
                .getSkepticalWineDetails({ id })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.ecdata = res.data.data.ecdata;
                        this.wcdata = res.data.data.wcdata;
                        //处理图片回显
                        this.showData.currentfileList =
                            res.data.data.ecdata.master_image !== ""
                                ? res.data.data.ecdata.master_image.split(",")
                                : [];
                        this.showData.oringinFilelist = res.data.data.wcdata
                            .master_image
                            ? res.data.data.wcdata.master_image.split(",")
                            : [];
                        //评分回显
                        this.showData.currentScores = this.ArrayToString(
                            res.data.data.ecdata.scores
                        );
                        this.showData.originScores = this.ArrayToString(
                            res.data.data.wcdata.scores
                        );
                        //--------------
                        for (const key in this.EXCUTE_PARAMS) {
                            if (
                                this.ecdata[this.EXCUTE_PARAMS[key]] !==
                                this.wcdata[this.EXCUTE_PARAMS[key]]
                            ) {
                                this.CHANGE_PARAMS[key] = true;
                            }
                        }
                        if (
                            this.showData.currentScores !==
                            this.showData.originScores
                        ) {
                            this.CHANGE_PARAMS.scores = true;
                        } else {
                            this.CHANGE_PARAMS.scores = false;
                        }
                    }
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.handle_container {
    display: flex;
    justify-content: space-between;
}
/deep/ .el-form-item {
    margin-bottom: 0px;
    .el-form-item__content {
        line-height: 20px;
    }
    .el-form-item__label {
        line-height: 20px;
        margin-bottom: 0;
    }
}
/deep/.el-checkbox {
    margin-bottom: 0;
}
.check_item_base {
    margin-bottom: 5px;
    min-height: 24px;
    padding: 3px 0 3px 8px;
}
.check_item {
    background: #e4f2da;
}
.red_text {
    color: red;
}
</style>

<template>
    <div>
        <el-form
            :model="wineData"
            ref="wineDataForm"
            :rules="rules"
            label-width="120px"
            :inline="false"
            size="normal"
        >
            <el-card shadow="hover">
                <div slot="header">
                    <span class="wineTitle">基础信息</span>
                </div>

                <el-form-item label="酒款图片" prop="image">
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="fileList"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="中文名" prop="winename">
                            <el-input
                                v-model="wineData.winename"
                                placeholder="请输入中文名"
                                class="w-220"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="英文名" prop="wename">
                            <el-input
                                v-model="wineData.wename"
                                placeholder="请输入英文名"
                                class="w-220"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12" :offset="0">
                        <el-form-item label="国家" prop="cid">
                            <el-select
                                v-model="wineData.cid"
                                filterable
                                clearable
                                placeholder="请选择国家"
                                @change="changeCountry"
                            >
                                <el-option
                                    v-for="item in CountryOptions"
                                    :key="item.id"
                                    :label="item.country_name_cn"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                            <el-select
                                style="margin-left: 10px; width: 280px"
                                v-model="wineData.aid"
                                filterable
                                remote
                                clearable
                                reserve-keyword
                                placeholder="请输入产区"
                                :remote-method="getRegionList"
                            >
                                <el-option
                                    v-for="item in RegionOptions"
                                    :key="item.id"
                                    :label="
                                        item.regions_name_cn +
                                        item.regions_name_en
                                    "
                                    :value="item.reparent + ',' + item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="品种类型" prop="colormid">
                            <el-select
                                v-model="wineData.colormid"
                                filterable
                                clearable
                                placeholder="请输入品种类型"
                            >
                                <el-option
                                    v-for="item in wineAscategoryList.vision"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12" :offset="0">
                        <el-form-item label="酒庄" prop="chaid">
                            <el-select
                                style="margin-left: 10px; width: 280px"
                                v-model="wineData.chaid"
                                filterable
                                remote
                                clearable
                                reserve-keyword
                                placeholder="请输入酒庄"
                                :remote-method="getWineryList"
                            >
                                <el-option
                                    v-for="item in WineryOptions"
                                    :key="item.id"
                                    :label="
                                        item.winery_name_cn +
                                        item.winery_name_en
                                    "
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="葡萄品种" prop="chaid">
                            <el-select
                                style="margin-left: 10px; width: 280px"
                                v-model="wineData.gid"
                                filterable
                                remote
                                clearable
                                multiple
                                reserve-keyword
                                placeholder="请输入葡萄品种"
                                :remote-method="getGrapeList"
                            >
                                <el-option
                                    v-for="item in GrapeOptions"
                                    :key="item.id"
                                    :label="item.gname_cn + item.gname_en"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12" :offset="0">
                        <el-form-item label="年份">
                            <el-input
                                v-model="wineData.wyear"
                                placeholder="请输入年份"
                                class="w-220"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="适饮期" prop="readyd_rink">
                            <el-input
                                v-model="wineData.readyd_rink"
                                placeholder="请输入适饮期"
                                class="w-220"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12" :offset="0">
                        <el-form-item label="参考价">
                            <el-input-number
                                :min="0"
                                :precision="2"
                                size="mini"
                                controls-position="right"
                                v-model="wineData.minprice"
                                placeholder="最低价格"
                                style="width: 100px"
                            ></el-input-number>
                            <span> - </span>
                            <el-input-number
                                size="mini"
                                :precision="2"
                                :min="0"
                                controls-position="right"
                                v-model="wineData.maxprice"
                                placeholder="最高价格"
                                style="width: 100px"
                            ></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="适酒温度">
                            <el-input
                                v-model="wineData.temper"
                                placeholder="请输入适酒温度"
                                style="width: 220px"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="wineTitle">详细信息</span>
                </div>

                <el-row>
                    <el-col :span="24" :offset="0">
                        <el-form-item label="混酿品种">
                            <el-select
                                v-model="brewayMethod"
                                placeholder="请选择酿造工艺"
                                clearable
                                @change="changeBreway"
                            >
                                <el-option
                                    v-for="item in wineBrewayList"
                                    :key="item.id"
                                    :label="item.title"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                            <el-input
                                style="margin-left: 20px; width: 80vh"
                                v-model="wineData.breway"
                                placeholder=""
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item prop="foodid" label="观色">
                    <!-- <el-form-item
                        v-for="i in this.wineAscategoryList.vision"
                        :key="i.id"
                        :label="i.name + ':'"
                    >
                        <el-checkbox-group v-model="wineData.colorid">
                            <el-checkbox
                                v-for="j in i.list"
                                :key="j.asid"
                                :label="j.asid"
                                >{{ j.cate_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item> -->
                    <el-form-item size="normal">
                        <el-select
                            v-model="color_option_value"
                            placeholder="请选择酒的品种"
                            @change="changeColorOption"
                        >
                            <el-option
                                v-for="item in this.wineAscategoryList.vision"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                        <el-select
                            style="margin-left: 20px"
                            v-model="wineData.colorid"
                            placeholder="请选择观色"
                            clearable
                        >
                            <el-option
                                v-for="item in color_option"
                                :key="item.asid"
                                :label="item.cate_name"
                                :value="item.asid"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form-item>
                <el-form-item prop="tasteid" label="口感">
                    <el-form-item
                        v-for="(i, key) in this.wineAscategoryList.taste"
                        :key="i.id"
                        :label="i.name + ':'"
                    >
                        <el-checkbox-group v-model="tasteid[key]" :max="1">
                            <el-checkbox
                                v-for="j in i.list"
                                :key="j.asid"
                                :label="j.asid"
                                >{{ j.cate_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>
                </el-form-item>
                <el-form-item label="基础香气">
                    <el-form-item
                        v-for="i in this.wineAscategoryList.smell"
                        :key="i.id"
                        :label="i.name + ':'"
                    >
                        <el-checkbox-group v-model="wineData.smellid">
                            <el-checkbox
                                v-for="j in i.list"
                                :key="j.asid"
                                :label="j.asid"
                                >{{ j.cate_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>
                </el-form-item>

                <el-form-item prop="foodid" label="餐食搭配">
                    <el-form-item
                        v-for="i in this.wineAscategoryList.Catering"
                        :key="i.id"
                        :label="i.name + ':'"
                    >
                        <el-checkbox-group v-model="wineData.foodid">
                            <el-checkbox
                                v-for="j in i.list"
                                :key="j.asid"
                                :label="j.asid"
                                >{{ j.cate_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>
                </el-form-item>
            </el-card>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss,
    },
    data() {
        return {
            wineData: {
                master_image: "",
                moddle_image: "",
                winename: "",
                wename: "",
                wyear: "",
                minprice: "",
                maxprice: "",
                readyd_rink: "",
                cid: "",
                gid: [],
                aid: "",
                chaid: "",
                colorid: "",
                colormid: "",
                smellid: [],
                tasteid: [],
                foodid: [],
                breway: "",
                temper: "",
            },
            color_option_value: "",
            color_option: [],
            rules: {
                wename: [
                    {
                        required: true,
                        message: "请输入英文名称",
                        trigger: "blur",
                    },
                ],
                // title:[
                //   { required: true, message: "请输入英文名称", trigger: "blur" },
                // ]
            },
            WineOptions: [],
            RegionOptions: [], //产区列表
            wineParamOptions: [],
            bgidOptions: [],
            CountryOptions: [],
            WineryOptions: [],
            GrapeOptions: [],
            wine_status: [
                {
                    label: "显示",
                    value: 1,
                },
                {
                    label: "不显示",
                    value: 0,
                },
            ],
            dir: "vinehoo/wiki/wine/",
            fileList: [],
            loading: false,
            isEdit: false,
            contentChangedKey: [],
            smellList: [],
            aromaList: [],
            selectedRegion: {},
            wineAscategoryList: {},
            wineBrewayList: [],
            wineBrewayOptions: {},
            brewayMethod: "",
            country_id: "",
            tasteid: new Array(5).fill([]),
        };
    },
    mounted() {
        this.getCountryList();
    },
    methods: {
        changeColorOption(value) {
            this.wineData.colorid = "";
            this.wineAscategoryList.vision.map((item) => {
                if (item.id === value) {
                    this.color_option = item.list;
                }
            });
        },
        changeCountry(id) {
            this.country_id = id;
            this.RegionOptions = [];
            this.wineData.aid = "";
        },
        getWineryList(keyword) {
            if (keyword) {
                this.$request.winery
                    .getWineryList({
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.WineryOptions = res.data.data.list;
                        }
                    });
            }
        },
        changeBreway(item) {
            this.wineData.breway = this.wineBrewayOptions[item];
        },
        getAscategorylist() {
            this.$request.winestyle.getAscategorylist().then((res) => {
                if (res.data.error_code == 0) {
                    this.wineAscategoryList = res.data.data.asc;
                    this.wineBrewayList = res.data.data.tmp;
                    res.data.data.tmp.map((item) => {
                        this.wineBrewayOptions[item.id] = item.message;
                    });
                }
            });
        },
        // eslint-disable-next-line no-unused-vars
        getCountryList() {
            this.$request.region.getContparam().then((res) => {
                if (res.data.error_code == 0) {
                    this.CountryOptions = res.data.data.country;
                }
            });
        },
        getContparam() {
            return new Promise((resolve, reject) => {
                this.$request.winestyle
                    .getAscategorylist()
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.wineAscategoryList = res.data.data.asc;
                            this.wineBrewayList = res.data.data.tmp;
                            res.data.data.tmp.map((item) => {
                                this.wineBrewayOptions[item.id] = item.message;
                            });
                            resolve();
                        } else {
                            reject();
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        getRegionList(keyword) {
            if (!this.country_id) {
                this.$message.warning("请先选择国家");
                return;
            }
            if (keyword) {
                this.$request.product
                    .getRegionList({
                        nation_id: this.country_id,
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.RegionOptions = res.data.data.list;
                        }
                    });
            }
        },
        getWineList(keyword) {
            if (keyword) {
                this.$request.product
                    .getWineList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.WineOptions = res.data.data.list;
                        }
                    });
            }
        },
        getGrapeList(keyword) {
            if (keyword) {
                this.$request.product
                    .getGrapeList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.GrapeOptions = res.data.data.list;
                        }
                    });
            }
        },
        submit() {
            this.$refs.wineDataForm.validate((valid) => {
                if (valid) {
                    let wineData = JSON.parse(JSON.stringify(this.wineData));
                    let tasteid = this.tasteid.flat(1);
                    wineData.master_image = this.fileList.join(",");
                    wineData.moddle_image = this.fileList.join(",");
                    wineData.aid = wineData.aid.split(",");
                    wineData.tasteid = tasteid;
                    let method = this.isEdit
                        ? "updateWinestyle"
                        : "addWinestyle";
                    this.$request.winestyle[method](wineData).then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success(
                                (method ? "编辑" : "添加") + "成功"
                            );
                            this.$emit("updateSuccess");
                        }
                    });
                }
            });
        },
        addWine() {
            this.getContparam();
        },
        editWine(id) {
            this.isEdit = true;
            this.getContparam().then(() => {
                this.$request.winestyle.getWineDetails({ id }).then((res) => {
                    if (res.data.error_code == 0) {
                        for (const item in res.data.data) {
                            for (const item2 in this.wineData) {
                                if (item == item2) {
                                    this.wineData[item] = res.data.data[item];
                                }
                            }
                        }
                        this.WineryOptions = res.data.data.winery;
                        this.RegionOptions = res.data.data.regions;
                        this.GrapeOptions = res.data.data.grape;
                        if (res.data.data.rid) {
                            this.RegionOptions = [res.data.data.regions];
                            this.wineData.rid = Number(res.data.data.rid);
                        }

                        if (res.data.data.cid) {
                            this.country_id = res.data.data.cid;
                        }
                        let senseArr = ["foodid", "smellid", "gid"];
                        senseArr.map((key) => {
                            if (res.data.data[key]) {
                                this.wineData[key] = res.data.data[key]
                                    .split(",")
                                    .map((item) => {
                                        return Number(item);
                                    });
                            } else {
                                this.wineData[key] = [];
                            }
                        });
                        if (res.data.data.tasteid) {
                            let tasteid = res.data.data.tasteid
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                            this.wineAscategoryList.taste.map((item, key) => {
                                item.list.map((taste_item) => {
                                    if (tasteid.includes(taste_item.asid)) {
                                        this.tasteid[key] = [taste_item.asid];
                                    }
                                });
                            });
                        }
                        if (res.data.data.colorid) {
                            this.wineAscategoryList.vision.map((i, key) => {
                                i.list.map((k) => {
                                    if (k.asid == res.data.data.colorid) {
                                        this.color_option = i.list;
                                        this.color_option_value = i.id;
                                        this.wineData.colorid = Number(
                                            res.data.data.colorid
                                        );
                                    }
                                });
                            });
                        }
                        //图片
                        if (this.wineData.master_image) {
                            this.fileList = [this.wineData.master_image];
                        }
                        let zeroArr = ["cid", "colormid", "chaid", "colorid"];
                        zeroArr.map((item) => {
                            if (res.data.data[item] == 0) {
                                this.wineData[item] = "";
                            }
                        });
                        this.wineData.id = res.data.data.id;
                    }
                });
            });
        },
        changeContent(index) {
            if (!this.contentChangedKey.includes(index)) {
                this.contentChangedKey.push(index);
                return;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.w-220 {
    width: 220px;
}
.wineTitle {
    font-size: 18px;
    font-weight: 600;
}
.avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.amap-demo {
    height: 300px;
}
</style>

<template>
    <div class="handle_container">
        <el-form
            :model="extraWineData"
            ref="wineDataForm"
            :rules="rules"
            label-width="120px"
            :inline="false"
            size="normal"
        >
            <el-card shadow="hover">
                <div slot="header">
                    <span class="wineTitle">基础信息</span>
                </div>

                <el-form-item label="酒款图片" prop="image">
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="8"
                        :dir="dir"
                        :file-list="master_image"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
                <el-form-item label="酒款缩略图" v-show="false">
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="8"
                        :dir="dir"
                        :file-list="moddle_image"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>

                <el-row>
                    <el-col :span="12">
                        <el-form-item label="中文名" prop="winename">
                            <el-input
                                v-model="extraWineData.winename"
                                placeholder="请输入中文名"
                                class="w-220"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="英文名" prop="wename">
                            <el-input
                                v-model="extraWineData.wename"
                                placeholder="请输入英文名"
                                class="w-220"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12" :offset="0">
                        <el-form-item label="国家" prop="cid">
                            <el-select
                                v-model="extraWineData.cid"
                                filterable
                                clearable
                                placeholder="请选择国家"
                                @change="changeCountry"
                            >
                                <el-option
                                    v-for="item in CountryOptions"
                                    :key="item.id"
                                    :label="item.country_name_cn"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                            <el-select
                                style="margin-left: 10px; width: 280px"
                                v-model="extraWineData.aid"
                                filterable
                                remote
                                clearable
                                reserve-keyword
                                placeholder="请输入产区"
                                :remote-method="getRegionList"
                            >
                                <el-option
                                    v-for="item in RegionOptions"
                                    :key="item.id"
                                    :label="
                                        item.regions_name_cn +
                                        item.regions_name_en
                                    "
                                    :value="item.reparent + ',' + item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="葡萄" prop="gid">
                            <el-select
                                v-model="extraWineData.gid"
                                filterable
                                remote
                                multiple
                                clearable
                                placeholder="请选择葡萄类型"
                                :remote-method="getGrapeList"
                            >
                                <el-option
                                    v-for="item in GrapeOptions"
                                    :key="item.id"
                                    :label="item.gname_cn + item.gname_en"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12" :offset="0">
                        <el-form-item label="酒庄" prop="chaid">
                            <el-select
                                style="margin-left: 10px; width: 280px"
                                v-model="extraWineData.chaid"
                                filterable
                                remote
                                clearable
                                placeholder="请输入酒庄"
                                :remote-method="getWineryList"
                            >
                                <el-option
                                    v-for="item in WineryOptions"
                                    :key="item.id"
                                    :label="
                                        item.winery_name_cn +
                                        item.winery_name_en
                                    "
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="年份">
                            <el-input
                                v-model="extraWineData.wyear"
                                placeholder="请输入年份"
                                class="w-220"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12" :offset="0">
                        <el-form-item label="适饮期" prop="readyd_rink">
                            <el-input
                                v-model="extraWineData.readyd_rink"
                                placeholder="请输入适饮期"
                                class="w-220"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="0">
                        <el-form-item label="适酒温度" prop="temper">
                            <el-input
                                v-model="extraWineData.temper"
                                placeholder="请输入适饮期"
                                class="w-220"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="20" :offset="0">
                        <el-form-item label="品酒词" prop="tasting">
                            <el-input
                                rows="4"
                                type="textarea"
                                v-model="extraWineData.tasting"
                                placeholder="请输入品酒词"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12" :offset="0">
                        <el-form-item label="评分" prop="scores">
                            <el-button
                                type="primary"
                                @click="addScore"
                                style="margin-bottom: 10px"
                                >添加</el-button
                            >

                            <div
                                style="margin-bottom: 10px"
                                v-for="(item, key) in extraWineData.scores"
                                :key="keyList[key]"
                            >
                                <el-input
                                    v-model="item.danx"
                                    placeholder="请输入奖项名称"
                                    class="w-220"
                                ></el-input>
                                <el-input
                                    style="margin-left: 20px"
                                    v-model="item.score"
                                    placeholder="请输入分数"
                                    class="w-220"
                                ></el-input>
                                <el-button
                                    type="danger"
                                    @click="delScore(key)"
                                    style="margin-left: 10px"
                                    >删除</el-button
                                >
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="wineTitle">详细信息</span>
                </div>

                <el-row>
                    <el-col :span="6" :offset="0">
                        <el-form-item label="混酿品种">
                            <el-select
                                v-model="brewayMethod"
                                placeholder="请选择酿造工艺"
                                clearable
                                @change="changeBreway"
                            >
                                <el-option
                                    v-for="item in wineBrewayList"
                                    :key="item.id"
                                    :label="item.title"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16" :offset="0">
                        <el-form-item label-width="0">
                            <el-input
                                type="textarea"
                                v-model="extraWineData.breway"
                            ></el-input> </el-form-item
                    ></el-col>
                </el-row>
                <el-form-item label="" size="normal">
                    <el-select
                        v-model="color_option_value"
                        placeholder="请选择酒的品种"
                        @change="changeColorOption"
                    >
                        <el-option
                            v-for="item in this.wineAscategoryList.vision"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                    <el-select
                        style="margin-left: 20px"
                        v-model="extraWineData.colorid"
                        placeholder="请选择观色"
                        clearable
                    >
                        <el-option
                            v-for="item in color_option"
                            :key="item.asid"
                            :label="item.cate_name"
                            :value="item.asid"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="tasteid" label="口感">
                    <el-form-item
                        v-for="(i, key) in this.wineAscategoryList.taste"
                        :key="i.id"
                        :label="i.name + ':'"
                    >
                        <el-checkbox-group v-model="tasteid[key]" :max="1">
                            <el-checkbox
                                v-for="j in i.list"
                                :key="j.asid"
                                :label="j.asid"
                                >{{ j.cate_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>
                </el-form-item>
                <el-form-item label="基础香气">
                    <el-form-item
                        v-for="i in this.wineAscategoryList.smell"
                        :key="i.id"
                        :label="i.name + ':'"
                    >
                        <el-checkbox-group v-model="extraWineData.smellid">
                            <el-checkbox
                                v-for="j in i.list"
                                :key="j.asid"
                                :label="j.asid"
                                v-model="extraWineData.smellid"
                                >{{ j.cate_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>
                </el-form-item>
                <el-form-item prop="foodid" label="餐食搭配">
                    <el-form-item
                        v-for="i in this.wineAscategoryList.Catering"
                        :key="i.id"
                        :label="i.name + ':'"
                    >
                        <el-checkbox-group v-model="extraWineData.foodid">
                            <el-checkbox
                                v-for="j in i.list"
                                :key="j.asid"
                                :label="j.asid"
                                >{{ j.cate_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>
                </el-form-item>
            </el-card>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss,
    },
    data() {
        return {
            extraWineData: {
                wename: "",
                winename: "",
                moddle_image: "",
                master_image: "",
                wyear: "",
                breway: "",
                gid: "",
                cid: "",
                aid: "",
                chaid: "",
                colorid: "",
                smellid: [],
                tasteid: [],
                foodid: [],
                readyd_rink: "",
                temper: "",
                tasting: "",
                drinking: "",
                scores: [],
            },
            moddle_image: [],
            master_image: [],
            rules: {},
            dir: "vinehoo/wiki/wine/",
            CountryOptions: [],
            RegionOptions: [],
            wineAscategoryList: [],
            WineryOptions: [],
            GrapeOptions: [],
            brewayMethod: "",
            wineBrewayList: [],
            region_parant: "",
            wineBrewayOptions: [],
            keyList: [this.uuid()],
            tasteid: new Array(5).fill([]),
            color_option_value: "",
            color_option: [],
        };
    },

    mounted() {
        this.getCountryList();
    },

    methods: {
        changeColorOption(value) {
            this.wineData.colorid = "";
            this.wineAscategoryList.vision.map((item) => {
                if (item.id === value) {
                    this.color_option = item.list;
                }
            });
        },
        uuid() {
            return `uuid-${new Date().getTime()}`;
        },
        changeExtraWineStatus() {
            this.$prompt("请输入要发放的兔头", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputPattern: /\b[1-9]\d{0,1}\b|\b[1]\d\d\b|\b200\b/,
                inputErrorMessage: "请输入1-200的兔头数量",
            }).then(({ value }) => {
                let data = JSON.parse(JSON.stringify(this.extraWineData));
                data.foodid = data.foodid.join(",");
                data.smellid = data.smellid.join(",");
                data.tasteid = this.tasteid.join(",");
                data.master_image = this.master_image.join(",");
                data.moddle_image = this.moddle_image.join(",");
                this.$request.winestyle
                    .changeExtraWineStatus({
                        ...data,
                        id: this.id,
                        status: 2,
                        rabbit: value,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.$message.success("操作成功");
                            this.$emit("updateSuccess");
                        }
                    });
            });
        },
        rejectWineDetailsStauts() {
            this.$confirm("确认拒绝该条修改吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                let data = {
                    id: this.id,
                    status: 3,
                };
                this.$request.winestyle
                    .changeExtraWineStatus(data)
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.$message.success("操作成功");
                            this.$emit("updateSuccess");
                        }
                    });
            });
        },
        getExtraDetails(id) {
            this.getAscategorylist().then(() => {
                this.id = id;
                this.$request.winestyle.getExtraDetails({ id }).then((res) => {
                    if (res.data.error_code === 0) {
                        for (const item in res.data.data) {
                            for (const item2 in this.extraWineData) {
                                if (item == item2) {
                                    this.extraWineData[item] =
                                        res.data.data[item];
                                }
                            }
                        }
                        this.master_image =
                            res.data.data.master_image !== ""
                                ? res.data.data.master_image.split(",")
                                : [];
                        this.moddle_image =
                            res.data.data.moddle_image !== ""
                                ? res.data.data.moddle_image.split(",")
                                : [];

                        if (res.data.data.gid) {
                            this.extraWineData.gid = res.data.data.gid
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                        }
                        let senseArr = ["foodid", "smellid"];
                        senseArr.map((key) => {
                            if (res.data.data[key]) {
                                this.extraWineData[key] = res.data.data[key]
                                    .split(",")
                                    .map((item) => {
                                        return Number(item);
                                    });
                            } else {
                                this.extraWineData[key] = [];
                            }
                        });
                        if (res.data.data.tasteid) {
                            let tasteid = res.data.data.tasteid
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                            this.wineAscategoryList.taste.map((item, key) => {
                                item.list.map((taste_item) => {
                                    if (tasteid.includes(taste_item.asid)) {
                                        this.tasteid[key] = [taste_item.asid];
                                    }
                                });
                            });
                        }
                        if (res.data.data.colorid) {
                            this.wineAscategoryList.vision.map((i, key) => {
                                i.list.map((k) => {
                                    if (k.asid == res.data.data.colorid) {
                                        this.color_option = i.list;
                                        this.color_option_value = i.id;
                                        this.extraWineData.colorid = Number(
                                            res.data.data.colorid
                                        );
                                    }
                                });
                            });
                        }
                        let zeroArr = ["chaid", "aid", "gid", "colorid"];
                        zeroArr.map((item) => {
                            if (!res.data.data[item]) {
                                this.extraWineData[item] = "";
                            }
                        });
                        let aid =
                            res.data.data.aid.split(",")[
                                res.data.data.aid.split(",").length - 1
                            ];
                        Promise.all([
                            this.getRegionList(aid, true),
                            this.getWineryList(res.data.data.chaid, true),
                            this.getGrapeList(res.data.data.gid, true),
                        ]);
                    }
                });
            });
        },
        getGrapeList(keyword, single) {
            return new Promise((resolve, reject) => {
                if (keyword) {
                    if (single) {
                        let grapeArr = keyword.split(",");
                        if (grapeArr.length > 0) {
                            grapeArr.map((item) => {
                                this.$request.grape
                                    .getGrapeDetails({
                                        id: item,
                                    })
                                    .then((res) => {
                                        if (res.data.error_code === 0) {
                                            this.GrapeOptions.push(
                                                res.data.data
                                            );
                                        }
                                    });
                            });
                            resolve();
                        }
                    } else {
                        this.$request.product
                            .getGrapeList({
                                keyword: keyword || "",
                                page: 1,
                                limit: 99,
                            })
                            .then((res) => {
                                if (res.data.error_code == 0) {
                                    this.GrapeOptions = res.data.data.list;
                                    resolve();
                                }
                            });
                    }
                }
            });
        },
        //酒庄
        getWineryList(keyword, single) {
            return new Promise((resolve, reject) => {
                if (keyword) {
                    if (single) {
                        this.$request.winery
                            .getWineryDetails({
                                id: keyword,
                            })
                            .then((res) => {
                                if (res.data.error_code == 0) {
                                    this.WineryOptions = [res.data.data];
                                    resolve();
                                }
                            });
                    } else {
                        this.$request.product
                            .getWineryList({
                                keyword: keyword || "",
                                page: 1,
                                limit: 10,
                            })
                            .then((res) => {
                                if (res.data.error_code == 0) {
                                    this.WineryOptions = res.data.data.list;
                                    resolve();
                                }
                            });
                    }
                }
            });
        },
        //产区
        getRegionList(keyword, single) {
            return new Promise((resolve, reject) => {
                if (keyword) {
                    if (single) {
                        this.$request.region
                            .getRegionDetails({
                                id: keyword,
                            })
                            .then((res) => {
                                if (res.data.error_code == 0) {
                                    this.RegionOptions = [res.data.data];
                                    resolve();
                                }
                            });
                    } else {
                        this.$request.product
                            .getRegionList({
                                keyword: keyword || "",
                                page: 1,
                                limit: 10,
                            })
                            .then((res) => {
                                if (res.data.error_code == 0) {
                                    this.RegionOptions = res.data.data.list;
                                    resolve();
                                }
                            });
                    }
                }
            });
        },
        //国家
        getCountryList(keyword) {
            return new Promise((resolve, reject) => {
                this.$request.product
                    .getCountryList({
                        page: 1,
                        limit: 200,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.CountryOptions = res.data.data.list;
                        }
                    });
            });
        },
        changeCountry(id) {
            this.country_id = id;
            this.RegionOptions = [];
            this.extraWineData.aid = "";
        },
        getAscategorylist() {
            return new Promise((resolve, reject) => {
                this.$request.winestyle.getAscategorylist().then((res) => {
                    console.warn(res.data.data.tmp);
                    res.data.data.tmp.map((item) => {
                        this.wineBrewayOptions[item.id] = item.message;
                    });
                    if (res.data.error_code == 0) {
                        this.wineAscategoryList = res.data.data.asc;
                        this.wineBrewayList = res.data.data.tmp;
                        res.data.data.tmp.map((item) => {
                            this.wineBrewayOptions[item.id] = item.message;
                        });
                        resolve();
                    }
                });
            });
        },
        addScore() {
            this.keyList.push(this.uuid());
            this.extraWineData.scores.push({
                danx: "",
                score: "",
            });
        },
        delScore(index) {
            this.keyList.splice(index, 1);
            this.extraWineData.scores.splice(index, 1);
        },
        changeContent(index) {
            if (!this.contentChangedKey.includes(index)) {
                this.contentChangedKey.push(index);
                return;
            }
        },
        changeBreway(item) {
            console.warn(item);
            this.extraWineData.breway = this.wineBrewayOptions[item];
        },
    },
};
</script>

<style lang="scss" scoped>
// .handle_container {
//     display: flex;
//     justify-content: space-between;
// }
// /deep/ .el-form-item {
//     margin-bottom: 0px;
//     .el-form-item__content {
//         line-height: 20px;
//     }
//     .el-form-item__label {
//         line-height: 20px;
//         margin-bottom: 0;
//     }
// }
// /deep/.el-checkbox {
//     margin-bottom: 0;
// }
// .check_item_base {
//     margin-bottom: 5px;
//     min-height: 24px;
//     padding: 3px 0 3px 8px;
// }
// .check_item {
//     background: #e4f2da;
// }
// .red_text {
//     color: red;
// }
.w-220 {
    width: 220px;
}
.handle_container {
    height: 700px;
    overflow-y: scroll;
}
</style>

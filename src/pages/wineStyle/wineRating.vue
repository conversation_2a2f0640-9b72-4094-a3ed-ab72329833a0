<template>
    <div>
        <el-button type="primary" size="mini" @click="addWineRating"
            >添加酒款评价</el-button
        >
        <el-table
            style="margin-top: 20px"
            :data="wineRatingList"
            border
            size="mini"
            :header-cell-style="{ 'text-align': 'center' }"
            :cell-style="{ 'text-align': 'center' }"
            height="400"
        >
            <el-table-column label="ID" prop="id" width="100">
            </el-table-column>
            <el-table-column label="用户昵称" prop="nickname" width="150">
            </el-table-column>
            <el-table-column label="用户ID" prop="uid" width="150">
            </el-table-column>
            <el-table-column label="评分" prop="score" width="100">
            </el-table-column>
            <el-table-column label="内容" prop="content" show-overflow-tooltip>
            </el-table-column>
            <el-table-column label="操作" width="100" fixed="right">
                <template slot-scope="{ row }">
                    <el-button
                        type="text"
                        size="mini"
                        @click="disableRate(row)"
                        >{{ row.status === 0 ? "显示" : "屏蔽" }}</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="wineRatingData.limit"
                :current-page="wineRatingData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            append-to-body
            title="添加评分"
            :visible.sync="addRatingVisible"
            width="40%"
            @close="closeAddRatingDialog"
        >
            <el-form
                :model="addRatingData"
                ref="addRatingRef"
                :rules="rules"
                label-width="100px"
                :inline="false"
                size="normal"
            >
                <el-form-item label="评分" prop="score">
                    <el-rate
                        v-model="addRatingData.score"
                        style="margin-top: 10px"
                    ></el-rate>
                </el-form-item>
                <el-form-item label="内容" prop="content">
                    <el-input
                        style="width: 320px"
                        type="textarea"
                        v-model="addRatingData.content"
                    ></el-input>
                </el-form-item>
                <el-form-item label="评论人" prop="uid">
                    <el-select
                        v-model="addRatingData.uid"
                        placeholder="请选择评论人"
                        clearable
                    >
                        <el-option
                            v-for="item in userOptions"
                            :key="item.id"
                            :label="item.nickname"
                            :value="item.uid"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>

            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="addRatingVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmAddWineRate"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "Vue2ProductLibraryWineRating",

    data() {
        return {
            wineRatingList: [],
            wineRatingData: {
                page: 1,
                limit: 10,
            },
            addRatingData: {
                wid: "",
                score: 5,
                content: "",
                eyear: "",
                uid: "",
            },
            addRatingVisible: false,
            rules: {
                score: [
                    {
                        required: true,
                        message: "请选择分数",
                        trigger: "change",
                    },
                ],
                content: [
                    {
                        required: true,
                        message: "请输入内容",
                        trigger: "change",
                    },
                ],
                uid: [
                    {
                        required: true,
                        message: "请选择发送人",
                        trigger: "change",
                    },
                ],
            },
            userOptions: [],
            total: 0,
        };
    },

    mounted() {
        this.getVestList();
    },

    methods: {
        closeAddRatingDialog() {
            this.addRatingData.score = 5;
            this.addRatingData.content = "";
            this.addRatingData.uid = "";
            this.$refs["addRatingRef"].resetFields();
        },
        addWineRating() {
            this.addRatingVisible = true;
        },
        getWineRatingList(row) {
            if (row?.id) {
                this.addRatingData.wid = row.id;
            }
            this.$request.winestyle
                .getWineComment({
                    ...this.wineRatingData,
                    wid: this.addRatingData.wid,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.wineRatingList = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        getVestList() {
            this.$request.winestyle.getVestList().then((res) => {
                if (res.data.error_code == 0) {
                    this.userOptions = res.data.data.list;
                }
            });
        },
        comfirmAddWineRate() {
            this.$refs["addRatingRef"].validate((valid) => {
                if (valid) {
                    let data = JSON.parse(JSON.stringify(this.addRatingData));
                    this.$request.winestyle
                        .createWineComment(data)
                        .then((res) => {
                            if (res.data.error_code === 0) {
                                this.$message.success("新增成功");
                                this.addRatingVisible = false;
                                this.getWineRatingList();
                            }
                        });
                }
            });
        },
        disableRate(row) {
            this.$confirm(
                `确认${row.status === 0 ? "显示" : "屏蔽"}此条记录吗?`,
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }
            ).then(() => {
                this.$request.winestyle
                    .changeWineCommentStatus({
                        id: row.id,
                        status: row.status === 0 ? 1 : 0,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            this.getWineRatingList();
                            this.$message({
                                type: "success",
                                message: "操作成功!",
                            });
                        }
                    });
            });
        },
        handleSizeChange(limit) {
            this.wineRatingData.limit = limit;
            this.wineRatingData.page = 1;
            this.getWineRatingList();
        },
        handleCurrentChange(page) {
            this.wineRatingData.page = page;
            this.getWineRatingList();
        },
    },
};
</script>

<style lang="scss" scoped></style>

<template>
    <div>
        <el-card shadow="always">
            <el-input
                v-model="queryCountryData.keyword"
                size="mini"
                class="m-r-10 w-normal"
                @keyup.enter.native="queryCountry"
                placeholder="请输入国家"
                clearable
            ></el-input>
            <el-button type="warning" size="mini" @click="queryCountry"
                >查询</el-button
            >
            <el-button type="success" size="mini" @click="addCountry"
                >新增</el-button
            >
            <!-- <el-button
            type="danger"
            @click="removeCountry"
            :disabled="multipleSelection.length == 0"
            >删除</el-button
          > -->
        </el-card>
        <el-card shadow="always" style="margin-top: 20px">
            <el-table
                :data="countryList"
                border
                size="mini"
                ref="multipleTable"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column label="编号" prop="id" width="100">
                </el-table-column>
                <el-table-column label="中文名" prop="country_name_cn">
                </el-table-column>
                <el-table-column label="英文名" prop="country_name_en">
                </el-table-column>
                <el-table-column label="国家介绍" width="450">
                    <template slot-scope="scope">
                        <span
                            style="
                                overflow: hidden;
                                text-overflow: ellipsis;
                                display: -webkit-box;
                                -webkit-line-clamp: 2;
                                -webkit-box-orient: vertical;
                            "
                            >{{ scope.row.summary }}</span
                        >
                    </template>
                </el-table-column>
                <el-table-column label="区域" prop="continent_id" width="100">
                </el-table-column>
                <el-table-column label="出口量" prop="export" width="100">
                </el-table-column>
                <el-table-column label="生产量" prop="capacity" width="100">
                </el-table-column>
                <el-table-column label="人均消费" prop="cpitacon" width="80">
                </el-table-column>
                <el-table-column label="热搜" prop="is_hot" width="80">
                    <template slot-scope="scope">
                        <el-tag
                            @click="
                                handleStatus(
                                    scope.row.id,
                                    scope.row.is_hot,
                                    'is_hot',
                                    scope.row.country_name_en
                                )
                            "
                            style="cursor: pointer"
                            :type="scope.row.is_hot == 1 ? 'success' : 'danger'"
                        >
                            {{ scope.row.is_hot == 1 ? "是" : "否" }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="状态" prop="country_status" width="80">
                    <template slot-scope="scope">
                        <el-tag
                            @click="
                                handleStatus(
                                    scope.row.id,
                                    scope.row.country_status,
                                    'country_status',
                                    scope.row.country_name_en
                                )
                            "
                            style="cursor: pointer"
                            :type="
                                scope.row.country_status == 1
                                    ? 'success'
                                    : 'danger'
                            "
                        >
                            {{
                                scope.row.country_status == 1
                                    ? "显示"
                                    : "不显示"
                            }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="排序" prop="sort" width="180">
                    <template slot-scope="scope">
                        <el-input-number
                            v-model="scope.row.sort"
                            size="mini"
                            clearable
                            @change="changeSort(scope.row)"
                        ></el-input-number>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="120">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="editCountry(scope.row)"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center; margin-top: 20px">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryCountryData.page"
                :page-size="queryCountryData.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            title="国家设置"
            :visible.sync="countryVisible"
            width="80%"
            @close="closeDialog"
            :close-on-click-modal="false"
        >
            <updateCountry
                ref="updateCountryRef"
                v-if="countryVisible"
                @updateSuccess="updateSuccess"
            ></updateCountry>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="countryVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmUpdateCountry"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import updateCountry from "./updateCountry.vue";
export default {
    components: { updateCountry },
    data() {
        return {
            queryCountryData: {
                keyword: "",
                page: 1,
                limit: 10,
            },
            countryList: [],
            total: 0,
            countryVisible: false,
            multipleSelection: [],
        };
    },

    mounted() {
        this.getCountryList();
    },

    methods: {
        queryCountry() {
            this.queryCountryData.page = 1;
            this.getCountryList();
        },
        handleStatus(id, fileds, fileds_name, country_name_en) {
            let params = {};
            if (fileds_name == "is_hot") {
                let is_hot = fileds == 1 ? 0 : 1;
                params = {
                    is_hot,
                    id,
                    country_name_en,
                };
            } else if (fileds_name == "country_status") {
                let country_status = fileds == 1 ? 0 : 1;
                params = {
                    id,
                    country_status,
                    country_name_en,
                };
            }
            this.$request.country.updateCountry(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.$message.success("编辑成功");
                    this.getCountryList();
                }
            });
        },
        handleSizeChange(val) {
            this.queryCountryData.limit = val;
            this.queryCountryData.page = 1;
            this.getCountryList();
        },
        handleCurrentChange(val) {
            this.queryCountryData.page = val;
            this.getCountryList();
        },
        modifyCountry(data) {
            this.$request.country.updateCountry(data).then((response) => {
                if (response.data.error_code == 0) {
                    this.$message.success("修改成功");
                    this.getCountryList();
                }
            });
        },
        changeSort(row) {
            this.modifyCountry({
                id: row.id,
                country_name_en: row.country_name_en,
                sort: row.sort,
            });
        },
        getCountryList() {
            this.$request.country
                .getCountryList(this.queryCountryData)
                .then((res) => {
                    this.countryList = res.data.data.list;
                    this.total = res.data.data.total;
                });
        },
        addCountry() {
            this.countryVisible = true;
        },
        comfirmUpdateCountry() {
            this.$nextTick(() => {
                this.$refs.updateCountryRef.submit();
            });
        },
        editCountry(row) {
            this.countryVisible = true;
            this.$nextTick(() => {
                this.$refs.updateCountryRef.editCountry(row.id);
                console.warn(row.id);
            });
        },
        closeDialog() {
            this.getCountryList();
        },
        updateSuccess() {
            this.countryVisible = false;
        },
        handleSelectionChange(val) {
            console.warn(val);
            this.multipleSelection = val;
        },
        removeCountry() {
            this.$confirm("是否删除数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                let ids = this.multipleSelection.map((item) => {
                    return item.id;
                });
                this.$request.country
                    .deleteCountry({
                        id: ids,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("删除成功");
                            this.getCountryList();
                        }
                    });
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>

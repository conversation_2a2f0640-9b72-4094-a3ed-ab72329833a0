<template>
    <div>
        <el-form
            :model="countryData"
            ref="countryDataForm"
            :rules="rules"
            label-width="80px"
            :inline="false"
            size="normal"
        >
            <el-card shadow="hover">
                <div slot="header">
                    <span class="countryTitle">基础信息</span>
                </div>

                <el-form-item label="中文名" prop="country_name_cn">
                    <el-input
                        v-model="countryData.country_name_cn"
                        placeholder="请输入中文名"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="英文名" prop="country_name_en">
                    <el-input
                        v-model="countryData.country_name_en"
                        placeholder="请输入英文名"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="代表葡萄" prop="represent_grape">
                    <el-select
                        v-model="countryData.represent_grape"
                        filterable
                        remote
                        style="width: 280px"
                        multiple
                        reserve-keyword
                        placeholder="请输入葡萄品种"
                        :remote-method="getGrapeList"
                    >
                        <el-option
                            v-for="item in GrapeOptions"
                            :key="item.id"
                            :label="item.gname_cn + item.gname_en"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="代表产区" prop="represent_area">
                    <el-select
                        v-model="countryData.represent_area"
                        filterable
                        remote
                        style="width: 280px"
                        clearable
                        multiple
                        reserve-keyword
                        placeholder="请输入产区"
                        :remote-method="getRegionList"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in RegionOptions"
                            :key="item.id"
                            :label="item.regions_name_cn + item.regions_name_en"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="代表酒款" prop="represent_wine">
                    <el-select
                        v-model="countryData.represent_wine"
                        filterable
                        remote
                        style="width: 280px"
                        clearable
                        multiple
                        reserve-keyword
                        placeholder="请输入酒款"
                        :remote-method="getWineList"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in WineOptions"
                            :key="item.id"
                            :label="item.winename + item.wename"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="区域" prop="continent_id">
                    <el-select
                        v-model="countryData.continent_id"
                        placeholder="请选择区域"
                        style="width: 280px"
                    >
                        <el-option
                            v-for="item in mainlandOptions"
                            :key="item"
                            :label="item"
                            :value="item"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="国家坐标">
                    <!-- 维度:lat;经度:long -->
                    <el-form-item label="经度" prop="long">
                        <el-input
                            v-model="countryData.long"
                            class="w-220"
                            placeholder="请输入经度"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="纬度" prop="lat">
                        <el-input
                            v-model="countryData.lat"
                            class="w-220"
                            placeholder="请输入纬度"
                        ></el-input>
                    </el-form-item>
                    <!-- <Map></Map> -->
                </el-form-item>
                <el-form-item label="国家图片" prop="image">
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="fileList"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
                <el-form-item label="状态" prop="country_status">
                    <el-radio-group v-model="countryData.country_status">
                        <el-radio
                            v-for="item in country_status"
                            :label="item.value"
                            :key="item.value"
                        >
                            {{ item.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="countryTitle">详细信息</span>
                </div>
                <el-form-item label="面积" prop="area">
                    <el-input-number
                        v-model="countryData.area"
                        :precision="2"
                        :min="0"
                        class="w-220"
                        placeholder="请输入面积"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="出口量" prop="export">
                    <el-input-number
                        :precision="2"
                        :min="0"
                        v-model="countryData.export"
                        class="w-220"
                        placeholder="请输入出口量"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="进口量" prop="import">
                    <el-input-number
                        :precision="2"
                        :min="0"
                        v-model="countryData.import"
                        class="w-220"
                        placeholder="请输入进口量"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="生产量" prop="capacity">
                    <el-input-number
                        :precision="2"
                        :min="0"
                        v-model="countryData.capacity"
                        class="w-220"
                        placeholder="请输入生产量"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="消费量" prop="consumption">
                    <el-input-number
                        :precision="2"
                        :min="0"
                        v-model="countryData.consumption"
                        class="w-220"
                        placeholder="请输入消费量"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="人均消费" prop="cpitacon">
                    <el-input-number
                        :precision="2"
                        :min="0"
                        v-model="countryData.cpitacon"
                        class="w-220"
                        placeholder="请输入人均消费"
                    ></el-input-number>
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="countryTitle">评价</span>
                </div>
                <el-form-item label-width="40px" prop="evaluate">
                    <el-input
                        rows="5"
                        type="textarea"
                        v-model="countryData.evaluate"
                    ></el-input>
                </el-form-item>
            </el-card>
            <!-- <el-card shadow="hover" style="margin-top: 20px">
        <div slot="header">
          <span class="countryTitle">级别介绍</span>
        </div>
        <el-form-item label-width="40px">
          <el-input
            type="textarea"
            v-model="countryData.country_name_en"
          ></el-input>
        </el-form-item>
      </el-card> -->
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="countryTitle">葡萄起源</span>
                </div>
                <el-form-item label-width="40px">
                    <el-button
                        type="primary"
                        size="default"
                        @click="addHisparam"
                        >新增</el-button
                    >
                </el-form-item>
                <el-form-item
                    label-width="40px"
                    v-for="(item, key) in countryData.hisparam"
                    :key="key"
                    :prop="'hisparam.' + key + '.title'"
                    :rules="{
                        required: true,
                        message: '请输入标题',
                        trigger: 'blur',
                    }"
                >
                    <el-input
                        v-model="item.title"
                        class="w-220"
                        @input="changeContent(item.id)"
                        placeholder="请输入标题"
                    ></el-input>
                    <el-input
                        v-model="item.content"
                        style="max-width: 500px; margin: 0 20px"
                        placeholder="请输入内容"
                        @input="changeContent(item.id)"
                    ></el-input>

                    <el-button
                        type="danger"
                        size="default"
                        @click="removeHisparam(key)"
                        >删除</el-button
                    >
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="countryTitle">概述资料</span>
                </div>
                <el-form-item label-width="40px" prop="summary">
                    <el-input
                        rows="5"
                        type="textarea"
                        v-model="countryData.summary"
                    ></el-input>
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="countryTitle">网页概述</span>
                </div>
                <el-form-item prop="summary_web" label-width="0">
                    <Tinymce
                        ref="editor"
                        v-model.trim="countryData.summary_web"
                        :height="300"
                    />
                </el-form-item>
            </el-card>
        </el-form>
    </div>
</template>

<script>
import Tinymce from "../../components/Tinymce/index";
import VosOss from "vos-oss";
// import Map from "../../components/Map.vue";
export default {
    components: {
        Tinymce,
        VosOss,
        // Map,
    },
    data() {
        return {
            countryData: {
                country_name_cn: "",
                country_name_en: "",
                represent_grape: "",
                represent_area: [],
                represent_wine: "",
                continent_id: "",
                country_status: "",
                long: "",
                lat: "",
                image: "",
                sort: "",
                summary: "",
                wine_nums: "",
                area_nums: "",
                member_id: "",
                area: 0,
                export: 0,
                import: 0,
                capacity: 0,
                consumption: 0,
                cpitacon: 0,
                evaluate: "",
                is_hot: "",
                is_finish: "",
                hisparam: [],
                summary_web: "",
            },
            rules: {
                country_name_en: [
                    {
                        required: true,
                        message: "请输入英文名称",
                        trigger: "blur",
                    },
                ],
                // title:[
                //   { required: true, message: "请输入英文名称", trigger: "blur" },
                // ]
            },
            GrapeOptions: [],
            RegionOptions: [], //产区列表
            WineOptions: [], //产区列表
            mainlandOptions: [],
            country_status: [
                {
                    label: "显示",
                    value: 1,
                },
                {
                    label: "不显示",
                    value: 0,
                },
            ],
            dir: "vinehoo/wiki/country/",
            fileList: [],
            loading: false,
            isEdit: false,
            contentChangedKey: [],
        };
    },
    mounted() {
        this.getContparam();
    },
    methods: {
        getContparam() {
            this.$request.country.getContparam().then((res) => {
                if (res.data.error_code == 0) {
                    this.mainlandOptions = res.data.data.mainland;
                }
            });
        },
        getRegionList(keyword) {
            if (keyword) {
                this.$request.product
                    .getRegionList({
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.RegionOptions = res.data.data.list;
                        }
                    });
            }
        },
        getGrapeList(keyword) {
            if (keyword) {
                this.$request.product
                    .getGrapeList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.GrapeOptions = res.data.data.list;
                        }
                    });
            }
        },
        getWineList(keyword) {
            if (keyword) {
                this.$request.country
                    .getWineList({
                        keyword: keyword,
                        fields: "id,winename,wename",
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.WineOptions = res.data.data.list;
                        }
                    });
            }
        },
        addHisparam() {
            this.countryData.hisparam.push({
                title: "",
                content: "",
            });
        },
        removeHisparam(index) {
            this.$confirm("是否删除该条数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                if (this.countryData.hisparam[index].id) {
                    this.$request.country
                        .deleteHistory({
                            country_id: this.countryData.id,
                            id: this.countryData.hisparam[index].id,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("删除成功");
                                this.contentChangedKey.map((item, key) => {
                                    if (
                                        item ==
                                        this.countryData.hisparam[index].id
                                    ) {
                                        this.contentChangedKey.splice(key, 1);
                                    }
                                });
                                this.countryData.hisparam.splice(index, 1);
                            }
                        });
                } else {
                    this.countryData.hisparam.splice(index, 1);
                }
            });
        },
        submit() {
            this.$refs.countryDataForm.validate((valid) => {
                if (valid) {
                    if (this.isEdit) {
                        this.countryData.hisparam.map((item) => {
                            if (item.id == undefined) {
                                this.$request.country.addHistory({
                                    title: item.title,
                                    content: item.content,
                                    country_id: this.countryData.id,
                                });
                            } else if (
                                this.contentChangedKey.includes(item.id)
                            ) {
                                this.$request.country
                                    .updateHistory({
                                        country_id: this.countryData.id,
                                        id: item.id,
                                        title: item.title,
                                        content: item.content,
                                    })
                                    .then((res) => {
                                        if (res.data.error_code != 0) {
                                            this.$message.error(
                                                res.data.error_msg
                                            );
                                        }
                                    });
                            }
                        });
                    }
                    this.countryData.image = this.fileList.join(",");
                    let method = this.isEdit ? "updateCountry" : "addCountry";
                    this.$request.country[method](this.countryData).then(
                        (res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success(
                                    (method ? "编辑" : "添加") + "成功"
                                );
                                this.$emit("updateSuccess");
                            }
                        }
                    );
                }
            });
        },
        editCountry(id) {
            this.isEdit = true;
            this.$request.country.getCountryDetails({ id }).then((res) => {
                if (res.data.error_code == 0) {
                    for (const item in res.data.data) {
                        for (const item2 in this.countryData) {
                            if (item == item2) {
                                this.countryData[item] = res.data.data[item];
                            }
                        }
                    }
                    this.countryData.id = res.data.data.id;
                    if (res.data.data.represent_grape) {
                        this.GrapeOptions = res.data.data.grape;
                        this.countryData.represent_grape =
                            res.data.data.represent_grape
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                    } else {
                        this.countryData.represent_grape = [];
                    }
                    if (res.data.data.represent_wine) {
                        this.WineOptions = res.data.data.winec;
                        this.countryData.represent_wine =
                            res.data.data.represent_wine
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                    } else {
                        this.countryData.represent_wine = [];
                    }
                    if (res.data.data.represent_wine) {
                        this.RegionOptions = res.data.data.regions;
                        this.countryData.represent_area =
                            res.data.data.represent_area
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                    } else {
                        this.countryData.represent_area = [];
                    }

                    this.countryData.hisparam = res.data.data.history.map(
                        (item) => {
                            return {
                                id: item.id,
                                title: item.title,
                                content: item.content,
                            };
                        }
                    );
                    if (this.countryData.image) {
                        this.fileList = [this.countryData.image];
                    } else {
                        this.fileList = [];
                    }
                }
            });
        },
        changeContent(index) {
            if (!this.contentChangedKey.includes(index)) {
                this.contentChangedKey.push(index);
                return;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.w-220 {
    width: 220px;
}
.countryTitle {
    font-size: 18px;
    font-weight: 600;
}
.avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.amap-demo {
    height: 300px;
}
</style>
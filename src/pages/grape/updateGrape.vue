<template>
    <div>
        <el-form
            :model="grapeData"
            ref="grapeDataForm"
            :rules="rules"
            label-width="120px"
            :inline="false"
            size="normal"
        >
            <el-card shadow="hover">
                <div slot="header">
                    <span class="grapeTitle">基础信息</span>
                </div>

                <el-form-item label="图片" prop="image">
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="fileList"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
                <el-form-item label="中文名" prop="gname_cn">
                    <el-input
                        v-model="grapeData.gname_cn"
                        placeholder="请输入中文名"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="英文名" prop="gname_en">
                    <el-input
                        v-model="grapeData.gname_en"
                        placeholder="请输入英文名"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <!-- <el-form-item label="别名" prop="bnname">
                    <el-input
                        v-model="grapeData.bnname"
                        placeholder="请输入别名"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="品种类型" prop="gtype">
                    <el-select
                        v-model="grapeData.gtype"
                        placeholder="请输入品种类型"
                        clearable
                        filterable
                    >
                        <el-option label="白葡萄" :value="1"> </el-option>
                        <el-option label="红葡萄" :value="2"> </el-option>
                        <el-option label="其他" :value="3"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="起源地" prop="careaids">
                    <el-select
                        v-model="grapeData.careaids"
                        filterable
                        style="width: 280px"
                        clearable
                        placeholder="请选择起源地"
                    >
                        <el-option
                            v-for="item in CountryOptions"
                            :key="item.id"
                            :label="item.country_name_cn + item.country_name_en"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="混酿品种" prop="bgid">
                    <el-select
                        v-model="grapeData.bgid"
                        filterable
                        remote
                        clearable
                        style="width: 280px"
                        multiple
                        reserve-keyword
                        placeholder="请输入混酿品种"
                        :remote-method="getGrapeList"
                    >
                        <el-option
                            v-for="item in GrapeOptions"
                            :key="item.id"
                            :label="item.gname_cn + item.gname_en"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="中文首字母" prop="cn_letter">
                    <el-input
                        v-model="grapeData.cn_letter"
                        placeholder="请输入中文首字母"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="英文首字母" prop="en_letter">
                    <el-input
                        v-model="grapeData.en_letter"
                        placeholder="请输入英文首字母"
                        class="w-220"
                    ></el-input>
                </el-form-item> -->
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="grapeTitle">详细信息</span>
                </div>
                <el-form-item prop="color_id" label="色彩">
                    <el-select
                        v-model="grapeData.color_id"
                        placeholder="请选择色彩"
                        clearable
                        filterable
                    >
                        <el-option
                            v-for="item in grapeParamOptions.color"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="基础香气" size="smell_id">
                    <el-form-item
                        v-for="i in this.grapeParamOptions.aroma"
                        :key="i.id"
                        :label="i.name + ':'"
                    >
                        <el-checkbox-group v-model="grapeData.smell_id">
                            <el-checkbox
                                v-for="j in i.list"
                                :key="j.asid"
                                :label="j.asid"
                                v-model="grapeData.smell_id"
                                >{{ j.cate_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>
                </el-form-item>

                <el-form-item prop="taste_id" label="味觉">
                    <el-form-item
                        v-for="i in this.grapeParamOptions.smell"
                        :key="i.id"
                        :label="i.name + ':'"
                    >
                        <el-checkbox-group v-model="grapeData.taste_id">
                            <el-checkbox
                                v-for="j in i.list"
                                :key="j.asid"
                                :label="j.asid"
                                >{{ j.cate_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>
                </el-form-item>
                <el-form-item prop="food_id" label="主要配餐">
                    <el-form-item
                        v-for="i in this.grapeParamOptions.cate"
                        :key="i.id"
                        :label="i.name + ':'"
                    >
                        <el-checkbox-group v-model="grapeData.food_id">
                            <el-checkbox
                                v-for="j in i.list"
                                :key="j.asid"
                                :label="j.asid"
                                >{{ j.cate_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>
                </el-form-item>
                <el-form-item label="典型产区">
                    <el-select
                        v-model="grapeData.rid"
                        filterable
                        remote
                        style="width: 280px"
                        clearable
                        reserve-keyword
                        placeholder="请输入产区"
                        :remote-method="getRegionList"
                    >
                        <el-option
                            v-for="item in RegionOptions"
                            :key="item.id"
                            :label="item.regions_name_cn + item.regions_name_en"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="grapeTitle">概述资料</span>
                </div>
                <el-form-item label-width="40px" prop="summary">
                    <el-input
                        rows="5"
                        type="textarea"
                        v-model="grapeData.summary"
                    ></el-input>
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="grapeTitle">网页概述</span>
                </div>
                <el-form-item prop="summary_web" label-width="0">
                    <Tinymce
                        ref="editor"
                        v-model.trim="grapeData.summary_web"
                        :height="300"
                    />
                </el-form-item>
            </el-card>
        </el-form>
    </div>
</template>

<script>
import Tinymce from "../../components/Tinymce/index";
import VosOss from "vos-oss";
// import Map from "../../components/Map.vue";
export default {
    components: {
        Tinymce,
        VosOss,
        // Map,
    },
    data() {
        return {
            grapeData: {
                image: "",
                gname_en: "",
                gname_cn: "",
                bnname: "",
                gtype: "",
                careaids: "",
                bgid: [],
                cn_letter: "",
                en_letter: "",
                color_id: "",
                smell_id: [],
                taste_id: [],
                food_id: [],
                rid: [],
                summary: "",
                summary_web: "",
            },
            rules: {
                gname_en: [
                    {
                        required: true,
                        message: "请输入英文名称",
                        trigger: "blur",
                    },
                ],
                // title:[
                //   { required: true, message: "请输入英文名称", trigger: "blur" },
                // ]
            },
            GrapeOptions: [],
            RegionOptions: [], //产区列表
            WineOptions: [], //产区列表
            grapeParamOptions: [],
            bgidOptions: [],
            CountryOptions: [],
            grape_status: [
                {
                    label: "显示",
                    value: 1,
                },
                {
                    label: "不显示",
                    value: 0,
                },
            ],
            dir: "vinehoo/wiki/grape/",
            fileList: [],
            loading: false,
            isEdit: false,
            contentChangedKey: [],
            smellList: [],
            aromaList: [],
            selectedRegion: {},
        };
    },
    mounted() {
        // console.warn(!this.isEdit);
        // if (!this.isEdit) {
        //   this.getContparam();
        this.getCountryList();
        // }
    },
    methods: {
        // eslint-disable-next-line no-unused-vars
        getCountryList() {
            this.$request.region.getContparam().then((res) => {
                if (res.data.error_code == 0) {
                    this.CountryOptions = res.data.data.country;
                }
            });
        },
        getContparam() {
            return new Promise((resolve, reject) => {
                this.$request.grape
                    .getContparam()
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.grapeParamOptions = res.data.data;
                            resolve();
                        } else {
                            reject();
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        getRegionList(keyword) {
            if (keyword) {
                this.$request.product
                    .getRegionList({
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.RegionOptions = res.data.data.list;
                        }
                    });
            }
        },
        getGrapeList(keyword) {
            if (keyword) {
                this.$request.product
                    .getGrapeList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.GrapeOptions = res.data.data.list;
                        }
                    });
            }
        },
        submit() {
            this.$refs.grapeDataForm.validate((valid) => {
                if (valid) {
                    let grapeData = JSON.parse(JSON.stringify(this.grapeData));
                    grapeData.image = this.fileList.join(",");
                    //bgid
                    if (!grapeData.bgid) {
                        grapeData.bgid = [];
                    }
                    if (grapeData.rid) {
                        grapeData.rid = [grapeData.rid];
                    } else {
                        grapeData.rid = [];
                    }
                    let method = this.isEdit ? "updateGrape" : "addGrape";
                    this.$request.grape[method](grapeData).then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success(
                                (method ? "编辑" : "添加") + "成功"
                            );
                            this.$emit("updateSuccess");
                        }
                    });
                }
            });
        },
        addGrape() {
            this.getContparam();
        },
        editGrape(id) {
            this.isEdit = true;
            this.getContparam().then(() => {
                this.$request.grape.getGrapeDetails({ id }).then((res) => {
                    if (res.data.error_code == 0) {
                        for (const item in res.data.data) {
                            for (const item2 in this.grapeData) {
                                if (item == item2) {
                                    this.grapeData[item] = res.data.data[item];
                                }
                            }
                        }
                        if (res.data.data.bgid) {
                            this.GrapeOptions = res.data.data.blended;
                            this.grapeData.bgid = res.data.data.bgid
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                        }
                        if (res.data.data.rid) {
                            this.RegionOptions = [res.data.data.regions];
                            this.grapeData.rid = Number(res.data.data.rid);
                        }
                        if (res.data.data.color_id == 0) {
                            this.grapeData.color_id = "";
                        }
                        if (res.data.data.gtype == 0) {
                            this.grapeData.gtype = "";
                        }
                        if (res.data.data.smell_id) {
                            this.grapeData.smell_id = res.data.data.smell_id
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                        } else {
                            this.grapeData.smell_id = [];
                        }
                        if (this.grapeData.careaids) {
                            this.grapeData.careaids = Number(
                                res.data.data.careaids
                            );
                        }
                        if (res.data.data.taste_id) {
                            this.grapeData.taste_id = res.data.data.taste_id
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                        } else {
                            this.grapeData.taste_id = [];
                        }
                        if (res.data.data.food_id) {
                            this.grapeData.food_id = res.data.data.food_id
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                        } else {
                            this.grapeData.food_id = [];
                        }
                        this.grapeData.id = res.data.data.id;
                        if (this.grapeData.image) {
                            this.fileList = [this.grapeData.image];
                        } else {
                            this.fileList = [];
                        }
                    }
                });
            });
        },
        changeContent(index) {
            if (!this.contentChangedKey.includes(index)) {
                this.contentChangedKey.push(index);
                return;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.w-220 {
    width: 220px;
}
.grapeTitle {
    font-size: 18px;
    font-weight: 600;
}
.avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.amap-demo {
    height: 300px;
}
</style>

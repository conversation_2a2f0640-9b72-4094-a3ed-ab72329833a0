<template>
    <div>
        <el-card shadow="always">
            <el-input
                size="mini"
                class="m-r-10 w-normal"
                @keyup.enter.native="queryGrape"
                v-model="queryGrapeData.keyword"
                placeholder="请输入葡萄品种名称"
                clearable
            ></el-input>
            <el-button type="warning" size="mini" @click="queryGrape"
                >查询</el-button
            >
            <el-button type="success" size="mini" @click="addGrape"
                >新增</el-button
            >
            <!-- <el-button
            type="danger"
            @click="removeGrape"
            :disabled="multipleSelection.length == 0"
            >删除</el-button
          > -->
        </el-card>
        <el-card shadow="always" style="margin-top: 20px">
            <el-table
                :data="grapeList"
                border
                size="mini"
                ref="multipleTable"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column label="编号" prop="id" width="100">
                </el-table-column>
                <el-table-column label="图片" prop="image" width="100">
                    <template slot-scope="scope">
                        <el-image
                            style="width: 80px; height: 80px"
                            :src="scope.row.image"
                            fit="fit"
                        >
                            <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i></div
                        ></el-image>
                        <!-- <el-image>
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image> -->
                    </template>
                </el-table-column>
                <el-table-column label="中文名" prop="gname_cn">
                </el-table-column>
                <el-table-column label="英文名" prop="gname_en">
                </el-table-column>
                <el-table-column label="品种类型" prop="gtype" width="100">
                    <template slot-scope="scope">
                        {{ gtypeOption[scope.row.gtype] }}
                    </template>
                </el-table-column>
                <el-table-column label="起源地" prop="careaids" width="180">
                    <template slot-scope="scope">
                        {{
                            scope.row.carea
                                ? scope.row.carea.regions_name_cn
                                : ""
                        }}
                    </template>
                </el-table-column>
                <el-table-column label="热搜" width="80" prop="is_hot">
                    <template slot-scope="scope">
                        <el-tag
                            @click="
                                handleStatus(
                                    scope.row.id,
                                    scope.row.is_hot,
                                    'is_hot',
                                    scope.row.grape_name_en
                                )
                            "
                            style="cursor: pointer"
                            :type="scope.row.is_hot == 1 ? 'success' : 'danger'"
                        >
                            {{ scope.row.is_hot == 1 ? "是" : "否" }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="120">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="editGrape(scope.row)"
                        >
                            编辑
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center; margin-top: 20px">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryGrapeData.page"
                :page-size="queryGrapeData.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            title="葡萄品种设置"
            :visible.sync="grapeVisible"
            width="80%"
            @close="closeDialog"
            :close-on-click-modal="false"
        >
            <updateGrape
                ref="updateGrapeRef"
                v-if="grapeVisible"
                @updateSuccess="updateSuccess"
            ></updateGrape>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="grapeVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmUpdateGrape"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import updateGrape from "./updateGrape.vue";
export default {
    components: { updateGrape },
    data() {
        return {
            queryGrapeData: {
                keyword: "",
                page: 1,
                limit: 10,
            },
            grapeList: [],
            total: 0,
            grapeVisible: false,
            multipleSelection: [],
            gtypeOption: {
                //1白葡萄 2红葡萄 3其他
                1: "白葡萄",
                2: "红葡萄",
                3: "其他",
            },
        };
    },

    mounted() {
        this.getGrapeList();
    },
    methods: {
        queryGrape() {
            this.queryGrapeData.page = 1;
            this.getGrapeList();
        },
        handleStatus(id, fileds, fileds_name, grape_name_en) {
            let params = {};
            if (fileds_name == "is_hot") {
                let is_hot = fileds == 1 ? 0 : 1;
                params = {
                    is_hot,
                    id,
                    grape_name_en,
                };
            } else if (fileds_name == "is_finish") {
                let is_finish = fileds == 1 ? 0 : 1;
                params = {
                    id,
                    is_finish,
                    grape_name_en,
                };
            } else if (fileds_name == "is_index") {
                let is_index = fileds == 1 ? 0 : 1;
                params = {
                    id,
                    is_index,
                    grape_name_en,
                };
            }
            this.$request.grape.updateGrape(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.$message.success("编辑成功");
                    this.getGrapeList();
                }
            });
        },
        handleSizeChange(val) {
            this.queryGrapeData.limit = val;
            this.queryGrapeData.page = 1;
            this.getGrapeList();
        },
        handleCurrentChange(val) {
            this.queryGrapeData.page = val;
            this.getGrapeList();
        },
        // changeHot(row) {
        //   this.$request.grape
        //     .updateGrape({
        //       id: row.id,
        //       is_hot: row.is_hot,
        //     })
        //     .then((response) => {
        //       if (response.data.code == 0) {
        //         this.$message.success("修改成功");
        //         this.getGrapeList();
        //       }
        //     });
        // },
        modifyGrape(data) {
            this.$request.grape.updateGrape(data).then((response) => {
                if (response.data.error_code == 0) {
                    this.$message.success("修改成功");
                    this.getGrapeList();
                }
            });
        },
        changeSort(row) {
            this.modifyGrape({
                id: row.id,
                grape_name_en: row.grape_name_en,
                sort: row.sort,
            });
        },
        getGrapeList() {
            this.$request.grape
                .getGrapeList(this.queryGrapeData)
                .then((res) => {
                    this.grapeList = res.data.data.list;
                    this.total = res.data.data.total;
                });
        },
        addGrape() {
            this.grapeVisible = true;
            this.$nextTick(() => {
                this.$refs.updateGrapeRef.addGrape();
            });
        },
        comfirmUpdateGrape() {
            this.$nextTick(() => {
                this.$refs.updateGrapeRef.submit();
            });
        },
        editGrape(row) {
            this.grapeVisible = true;
            this.$nextTick(() => {
                this.$refs.updateGrapeRef.editGrape(row.id);
                console.warn(row.id);
            });
        },
        closeDialog() {
            this.getGrapeList();
        },
        updateSuccess() {
            this.grapeVisible = false;
        },
        handleSelectionChange(val) {
            console.warn(val);
            this.multipleSelection = val;
        },
        removeGrape() {
            this.$confirm("是否删除数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                let ids = this.multipleSelection.map((item) => {
                    return item.id;
                });
                this.$request.grape
                    .deleteGrape({
                        id: ids,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("删除成功");
                            this.getGrapeList();
                        }
                    });
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.el-image {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    font-size: 20px;
    .image-slot {
    }
}
</style>

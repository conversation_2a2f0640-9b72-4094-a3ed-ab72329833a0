<template>
  <div class="upload-customs-container">
    <el-form :model="uploadForm" :rules="rules" ref="uploadForm" label-width="80px">
      <el-form-item label="产品简码" prop="shortCode">
        <el-select
          v-model="uploadForm.shortCode"
          filterable
          remote
          multiple
          style="width: 100%;"
          placeholder="请输入简码"
          :remote-method="getProducts"
          :loading="loading"
        >
          <el-option
            v-for="item in productOptions"
            :key="item.id"
            :label="item.short_code"
            :value="item.short_code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="上传文件">
        <div class="upload-section">
          <vos-oss
            :dir="dir"
            :file-list="fileList"
            :limit="6"
            :fileSize="10"
            :multiple="true"
            list-type="text"
            filesType=""
            :showFileList="true"
            :is_download="true"
            :showName="true"
          >
            <el-button size="small" type="primary">上传文件</el-button>
          </vos-oss>
          <div class="upload-tips">
            <div>最多上传6个文件</div>
            <div>文件名不能包含./\-_等特殊字符</div>
          </div>
        </div>
      </el-form-item>

      <el-form-item>
        <div class="btn-container">
          <el-button type="primary" @click="handleSubmit">保存</el-button>
          <el-button @click="closeDialog">取消</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import vosOss from "vos-oss";

export default {
  name: 'UploadCustomsDeclaration',
  components: { vosOss },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dir: "vinehoo/wiki/customsdeclaration/",
      loading: false,
      uploadForm: {
        shortCode: [],
      },
      rules: {
        shortCode: [
          { required: true, message: '请选择产品简码', trigger: 'change' }
        ]
      },
      productOptions: [],
      fileList: [],
      ossUploadUrl: '',
      ossData: {}
    }
  },

  methods: {
    async getProducts(query) {
      if (query) {
        this.$request.product
                .getProductList({  short_code: query })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.productOptions = res.data.data.list;
                    }
                });
      }
    },

    resetData() {
      this.uploadForm.shortCode = []
      this.productOptions = []
      this.fileList = []
    },

    closeDialog() {
      this.$emit('update:visible', false)
      this.resetData()
    },

    handleSubmit() {
      this.$refs.uploadForm.validate((valid) => {
        if (valid) {
          if (this.fileList.length === 0) {
            this.$message.error('请上传文件')
            return
          }
          const params = {
            short_code: this.uploadForm.shortCode.join(','),
            product_attachment: this.fileList.join(',')
          }
          this.$request.product
            .uploadBatchUpdateAttachment(params)
            .then((res) => {
              if (res.data.error_code == 0) {
                this.$emit('update:visible', false)
                this.$message.success('上传成功')
                this.resetData()
              }
            })
        }
      })
    }
  }
}
</script>

<style scoped>
.upload-customs-container {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}

.upload-section {
  width: 100%;
}

.upload-tips {
  color: #999999;
  font-size: 12px;
  margin-top: 10px;
  line-height: 1.5;
}

.el-form {
  max-width: 600px;
}

.btn-container {
  text-align: center;
  margin-top: 20px;
}

.btn-container .el-button {
  margin: 0 10px;
}
</style>

<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-form
                label-width="80px"
                :inline="true"
                @submit.native.prevent
                size="mini"
            >
                <el-form-item>
                    <el-input
                        @keyup.enter.native="queryKeyword"
                        v-model="queryKeywordData.keyword"
                        placeholder="请输入关键词"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryKeyword"
                        >查询</el-button
                    >
                    <el-button type="success" @click="updateKeyword"
                        >新增</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table
                :data="KeywordList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="关键词名称" prop="name" width="300px">
                </el-table-column> </el-table
        ></el-card>
        <el-dialog
            :close-on-click-modal="false"
            title="关键词设置"
            :visible.sync="keywordVisible"
            width="40%"
            @close="closeKeyword"
        >
            <el-form
                :model="KeywordData"
                ref="KeywordData"
                :rules="rules"
                label-width="80px"
                :inline="false"
                size="normal"
            >
                <el-form-item label="关键词" prop="name">
                    <el-input v-model="KeywordData.name"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="keywordVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmUpdate"
                    >确定</el-button
                >
            </span>
        </el-dialog>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryKeywordData.page"
                :page-size="queryKeywordData.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
export default {
    name: "Vue2ProductLibraryKeyword",

    data() {
        return {
            queryKeywordData: {
                keyword: "",
                page: 1,
                limit: 10,
            },
            KeywordList: [],
            keywordVisible: false,
            rules: {
                name: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请输入关键词",
                    },
                ],
            },
            KeywordData: {
                name: "",
            },
            total: 0,
        };
    },

    mounted() {
        this.getKeyword();
    },

    methods: {
        queryKeyword() {
            this.queryKeywordData.page = 1;
            this.getKeyword();
        },
        handleSizeChange(limit) {
            this.queryKeywordData.limit = limit;
            this.queryKeywordData.page = 1;
            this.getKeyword();
        },
        handleCurrentChange(page) {
            this.queryKeywordData.page = page;
            this.getKeyword();
        },
        getKeyword() {
            this.$request.product
                .getKeyword(this.queryKeywordData)
                .then((result) => {
                    if (result.data.error_code == 0) {
                        this.KeywordList = result.data.data.list;
                        this.total = result.data.data.total;
                    }
                });
        },
        comfirmUpdate() {
            this.$refs["KeywordData"].validate((valid) => {
                if (valid) {
                    this.$request.product
                        .addKeyword(this.KeywordData)
                        .then((result) => {
                            if (result.data.error_code == 0) {
                                this.$message.success("操作成功");
                                this.getKeyword();
                                this.keywordVisible = false;
                            }
                        });
                }
            });
        },
        closeKeyword() {
            this.queryKeywordData.name = "";
        },
        updateKeyword() {
            this.keywordVisible = true;
        },
    },
};
</script>

<style lang="scss" scoped>
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>

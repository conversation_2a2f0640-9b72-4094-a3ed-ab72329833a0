<template>
    <!-- begin login -->
    <div class="login login-with-news-feed login_img">
        <!-- begin news-feed -->
        <div class="news-feed ">
            <div class="news-image "></div>
            <div class="news-caption">
                <h4 class="caption-title">
                    <b>Vinehoo Template Vue.js</b>
                </h4>
                <div>
                    Template Management System
                </div>
            </div>
            <div
                class="right-content"
                ref="rightContent"
                @click="formsClick(1)"
            >
                <!-- begin login-header -->
                <div class="login-header">
                    <div class="brand">
                        <span class="logo"></span>
                        <b>Vinehoo</b> Template
                        <small>Template StartUp</small>
                    </div>
                </div>
                <!-- end login-header -->
                <!-- begin login-content -->
                <div class="login-content">
                    <form class="margin-bottom-0">
                        <div class="form-group m-b-15">
                            <input
                                type="text"
                                class="form-control form-control-lg"
                                placeholder="账号"
                                v-model="forms.username"
                                required
                            />
                        </div>
                        <div class="form-group m-b-15">
                            <input
                                type="password"
                                v-model="forms.password"
                                class="form-control form-control-lg"
                                placeholder="密码"
                                required
                            />
                        </div>

                        <div class="login-buttons">
                            <button
                                @click="login"
                                :disabled="loading"
                                class="btn btn-success btn-block btn-lg"
                            >
                                {{ note }}
                            </button>
                        </div>
                        <!-- <div class="m-t-20 m-b-40 p-b-40 text-inverse">
                        Not a member yet? Click
                        <router-link to="/register" class="text-success"
                            >here</router-link
                        >
                        to register.
                    </div> -->
                        <hr />
                        <p class="text-center text-grey-darker">
                            &copy; Vinehoo {{ year }} 酒云网
                        </p>
                    </form>
                </div>
                <!-- end login-content -->
            </div>
        </div>
        <!-- end news-feed -->
        <!-- begin right-content -->

        <!-- end right-container -->
    </div>
    <!-- end login -->
</template>

<script>
import PageOptions from "../../config/PageOptions";
import { mapMutations } from "vuex";
export default {
    data() {
        return {
            loading: false,
            note: "登录",
            year: new Date().getFullYear(),
            forms: {
                username: "",
                password: ""
            }
        };
    },
    watch: {
        loading(val) {
            if (val) {
                this.note = "登录中";
            } else {
                this.note = "登录";
            }
        }
    },
    created() {
        PageOptions.pageEmpty = true;
        document.body.className = "bg-white";
    },
    beforeRouteLeave(to, from, next) {
        PageOptions.pageEmpty = false;
        document.body.className = "";
        next();
    },
    mounted() {
        setTimeout(() => {
            this.formsClick(0.88);
        }, 300);
        this.cookies.remove("token");
        this.setPermissions("");
    },
    methods: {
        ...mapMutations(["setPermissions"]),
        formsClick(value) {
            console.log();
            if (this.$refs.rightContent) {
                this.$refs.rightContent.style = `opacity: ${value}`;
            }
        },
        async login() {
            this.$router.push({ path: "/productmanage" });
            if (this.forms.username && this.forms.password) {
                this.loading = true;
                let data = {
                    ...this.forms
                    // request
                };
                try {
                    let res = await this.$request.account.login(data);
                    this.loading = false;
                    if (res.data.errorCode == 0) {
                        console.log(res);
                        this.cookies.set("token", res.data.data.token);
                        this.$router.push({ path: "/home" });
                    }
                } catch {
                    this.loading = false;
                    console.log("error Request");
                }
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.login_img {
    // background-image: url("");
}
.login.login-with-news-feed .right-content {
    opacity: 0;
    transition: ease 1.5s;
}
.news-feed {
    width: 100%;
}
.login.login-with-news-feed .right-content .login-header + .login-content {
    transition: 0.7 ease;
    z-index: 1000;
}
.login.login-with-news-feed .news-feed {
    height: auto;
}
</style>

<template>
    <div>
        <el-form
            :model="wineryData"
            ref="wineryDataForm"
            :rules="rules"
            label-width="100px"
            :inline="false"
            size="normal"
        >
            <el-card shadow="hover">
                <div slot="header">
                    <span class="wineryTitle">基础信息</span>
                </div>

                <el-form-item label="中文名" prop="winery_name_cn">
                    <el-input
                        v-model="wineryData.winery_name_cn"
                        placeholder="请输入中文名"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="英文名" prop="winery_name_en">
                    <el-input
                        v-model="wineryData.winery_name_en"
                        placeholder="请输入英文名"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="进口商" prop="importer">
                    <el-input
                        v-model="wineryData.importer"
                        placeholder="请输入进口商"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <!-- <el-form-item label="所属产区" prop="reparent">
          <el-select
            v-model="wineryData.reparent"
            filterable
            remote
            clearable
            multiple
            reserve-keyword
            placeholder="请输入产区"
            :remote-method="getSuperiorList"
            :loading="loading"
          >
            <el-option
              v-for="item in SuperiorOptions"
              :key="item.id"
              :label="item.winery_name_cn"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
                <el-form-item label="特殊分级" prop="classify_id">
                    <el-select
                        v-model="wineryData.classify_id"
                        filterable
                        clearable
                        remote
                        placeholder="请输入分类"
                        :loading="loading"
                        :remote-method="getWineryClassiftList"
                    >
                        <el-option
                            v-for="item in classifyOption"
                            :key="item.id"
                            :label="item.title"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                    <el-select
                        style="margin-left: 10px"
                        v-model="wineryData.grade_id"
                        filterable
                        clearable
                        placeholder="请选择分级"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in gradeOption"
                            :key="item.id"
                            :label="item.scname"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="wineryTitle">详细信息</span>
                </div>
                <el-form-item label="面积" prop="area">
                    <el-input
                        v-model="wineryData.area"
                        class="w-220"
                        placeholder="请输入面积"
                    ></el-input>
                </el-form-item>
                <el-form-item label="拥有者" prop="owner">
                    <el-input
                        v-model="wineryData.owner"
                        class="w-220"
                        placeholder="请输入拥有者"
                    ></el-input>
                </el-form-item>
                <el-form-item label="平均树龄" prop="tree_age">
                    <el-input
                        v-model="wineryData.tree_age"
                        class="w-220"
                        placeholder="请输入平均树龄"
                    ></el-input>
                </el-form-item>
                <el-form-item label="酒庄地址" prop="addr">
                    <el-input
                        v-model="wineryData.addr"
                        class="w-220"
                        placeholder="请输入光照"
                    ></el-input>
                </el-form-item>
                <el-form-item label="酒庄坐标">
                    <el-form-item label="经度" prop="long">
                        <el-input
                            v-model="wineryData.long"
                            class="w-220"
                            placeholder="请输入经度"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="纬度" prop="lat">
                        <el-input
                            v-model="wineryData.lat"
                            class="w-220"
                            placeholder="请输入纬度"
                        ></el-input>
                    </el-form-item>
                </el-form-item>
                <el-form-item label="酒庄电话" prop="telephone">
                    <el-input
                        v-model="wineryData.telephone"
                        class="w-220"
                        placeholder="请输入酒庄电话"
                    ></el-input>
                </el-form-item>
                <el-form-item label="酒庄网址" prop="web_url">
                    <el-input
                        v-model="wineryData.web_url"
                        class="w-220"
                        placeholder="请输入酒庄网址"
                    ></el-input>
                </el-form-item>
                <el-form-item label="接待时间" prop="reception_time">
                    <el-input
                        v-model="wineryData.reception_time"
                        class="w-220"
                        placeholder="请输入接待时间"
                    ></el-input>
                </el-form-item>
                <el-form-item label="代表葡萄" prop="represent_grape_id">
                    <el-select
                        v-model="wineryData.represent_grape_id"
                        filterable
                        remote
                        multiple
                        style="width: 280px"
                        reserve-keyword
                        placeholder="请输入葡萄品种"
                        :remote-method="getGrapeList"
                    >
                        <el-option
                            v-for="item in GrapeOptions"
                            :key="item.id"
                            :label="item.gname_cn + item.gname_en"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="wineryTitle">附件</span>
                </div>
                <vos-oss
                    list-type="text"
                    :showFileList="true"
                    :limit="99"
                    :dir="dir"
                    :file-list="fileList"
                    filesType="/"
                    :showName="true"
                    @on-success="handleSuccessAnnex"
                    @on-remove="handleRemoveAnnex"
                >
                    <el-button size="small" type="primary">点击上传</el-button>
                </vos-oss>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="wineryTitle">荣誉动态</span>
                </div>
                <!-- card body -->
                <el-form-item label-width="40px">
                    <el-button type="primary" size="default" @click="addHonarr"
                        >新增</el-button
                    >
                </el-form-item>
                <el-form-item
                    label-width="40px"
                    v-for="(item, key) in wineryData.honarr"
                    :key="key"
                    :prop="'honarr.' + key + '.honordy_name'"
                    :rules="{
                        required: true,
                        message: '请输入荣誉名称',
                        trigger: 'blur',
                    }"
                >
                    <el-card shadow="always">
                        <div
                            style="
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                            "
                        >
                            <div style="display: flex">
                                <div
                                    style="
                                        display: flex;
                                        flex-direction: column;
                                        height: 148px;
                                        justify-content: space-between;
                                        margin-right: 20px;
                                    "
                                >
                                    <el-input
                                        v-model="item.honordy_name"
                                        class="w-220"
                                        @input="changeHornorContent(item.id)"
                                        placeholder="请输入荣誉名称"
                                    ></el-input>
                                    <el-input
                                        v-model="item.honordy_time"
                                        class="w-220"
                                        @input="changeHornorContent(item.id)"
                                        placeholder="请输入荣誉时间"
                                    ></el-input>
                                    <el-input
                                        v-model="item.honordy_info"
                                        class="w-220"
                                        @input="changeHornorContent(item.id)"
                                        placeholder="请输入荣誉信息"
                                    ></el-input>
                                </div>
                                <vos-oss
                                    style="width: 148px"
                                    list-type="picture-card"
                                    :showFileList="true"
                                    :limit="1"
                                    :dir="dir"
                                    :file-list="item.honordy_image"
                                    @on-change="changeHornorContent(item.id)"
                                    @on-remove="changeHornorContent(item.id)"
                                >
                                    <i slot="default" class="el-icon-plus"></i>
                                </vos-oss>
                            </div>
                            <div>
                                <el-button
                                    type="danger"
                                    size="default"
                                    @click="removeHonarr(key)"
                                    >删除</el-button
                                >
                            </div>
                        </div>
                    </el-card>
                </el-form-item>
            </el-card>

            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="wineryTitle">历史</span>
                </div>
                <el-form-item label-width="40px">
                    <el-button
                        type="primary"
                        size="default"
                        @click="addHisparam"
                        >新增</el-button
                    >
                </el-form-item>
                <el-form-item
                    label-width="40px"
                    v-for="(item, key) in wineryData.hisarr"
                    :key="key"
                    :prop="'hisarr.' + key + '.title'"
                    :rules="{
                        required: true,
                        message: '请输入标题',
                        trigger: 'blur',
                    }"
                >
                    <el-input
                        v-model="item.title"
                        class="w-220"
                        @input="changeContent(item.id)"
                        placeholder="请输入年份"
                    ></el-input>
                    <el-input
                        style="margin: 0 20px"
                        v-model="item.content"
                        class="w-220"
                        @input="changeContent(item.id)"
                        placeholder="请输入描述"
                    ></el-input>
                    <el-button
                        type="danger"
                        size="default"
                        @click="removeHisparam(key)"
                        >删除</el-button
                    >
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="wineryTitle">概述资料</span>
                </div>
                <el-form-item label-width="40px" prop="summary">
                    <el-input
                        rows="5"
                        type="textarea"
                        v-model="wineryData.summary"
                    ></el-input>
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="wineryTitle">网页概述</span>
                </div>
                <el-form-item prop="summary_web" label-width="0">
                    <Tinymce
                        ref="editor"
                        v-model.trim="wineryData.summary_web"
                        :height="300"
                    />
                </el-form-item>
            </el-card>
            <el-form-item
                label="资料完成"
                prop="is_finish"
                style="margin-top: 20px"
            >
                <el-radio-group v-model="wineryData.is_finish">
                    <el-radio
                        v-for="item in is_finish"
                        :label="item.value"
                        :key="item.value"
                    >
                        {{ item.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import Tinymce from "../../components/Tinymce/index";
import VosOss from "vos-oss";
// soil Map from "../../components/Map.vue";
export default {
    components: {
        Tinymce,
        // eslint-disable-next-line vue/no-unused-components
        VosOss,
        // Map,
    },
    data() {
        return {
            wineryData: {
                winery_name_cn: "",
                winery_name_en: "",
                importer: "",
                classify_id: "",
                area: "",
                owner: "",
                tree_age: "",
                addr: "",
                long: "",
                lat: "",
                hisarr: [],
                honarr: [],
                telephone: "",
                web_url: "",
                reception_time: "",
                represent_grape_id: "",
                summary: "",
                is_finish: "",
                grade_id: "",
                summary_web: "",
                annexarr: [],
            },
            rules: {
                winery_name_en: [
                    {
                        required: true,
                        message: "请输入英文名称",
                        trigger: "blur",
                    },
                ],
                // title:[
                //   { required: true, message: "请输入英文名称", trigger: "blur" },
                // ]
            },
            GrapeOptions: [], //葡萄列表
            classifyOption: [], //分类列表
            gradeOption: [], //等级列表
            is_finish: [
                {
                    label: "已完成",
                    value: 1,
                },
                {
                    label: "未完成",
                    value: 0,
                },
            ],
            dir: "vinehoo/wiki/winery/",
            fileList: [],
            loading: false,
            isEdit: false,
            contentChangedKey: [],
            honorContentChangedKey: [],
            removeAnnex: [],
            addAnnex: [],
        };
    },
    mounted() {
        this.getWineryGradeList();
        this.getWineryClassiftList();
    },
    methods: {
        // eslint-disable-next-line no-unused-vars
        handleRemoveAnnex(file) {
            if (this.wineryData.annexarr[file.index] != undefined) {
                this.removeAnnex.push(this.wineryData.annexarr[file.index].id);
            }
            this.wineryData.annexarr.splice(file.index, 1);
        },
        handleSuccessAnnex(file) {
            this.addAnnex.push({ path: file.file });
        },
        getWineryGradeList() {
            this.$request.winery.getWineryGrade().then((res) => {
                this.gradeOption = res.data.data.list;
            });
        },
        getWineryClassiftList() {
            this.$request.winery
                .getWineryClassify({
                    page: 1,
                    limit: 100,
                })
                .then((res) => {
                    this.classifyOption = res.data.data.list;
                });
        },
        getSuperiorList(keyword) {
            if (keyword) {
                if (this.wineryData.reparent) {
                    this.wineryData.reparent = parseInt(
                        this.wineryData.reparent
                    );
                }
                this.$request.product
                    .getWineryList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.SuperiorOptions = res.data.data.list;
                        }
                    });
            }
        },
        getGrapeList(keyword) {
            if (keyword) {
                this.$request.product
                    .getGrapeList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.GrapeOptions = res.data.data.list;
                        }
                    });
            }
        },

        getWineList(keyword) {
            if (keyword) {
                this.$request.winery
                    .getWineList({
                        keyword: keyword,
                        fields: "id,winename,wename",
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.WineOptions = res.data.data.list;
                        }
                    });
            }
        },
        addHisparam() {
            this.wineryData.hisarr.push({
                title: "",
                content: "",
            });
        },
        addHonarr() {
            this.wineryData.honarr.push({
                honordy_name: "",
                honordy_image: [],
                honordy_time: "",
                honordy_info: "",
            });
        },
        removeHisparam(index) {
            this.$confirm("是否删除该条数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                //判断历史参数是否是新增的
                if (this.wineryData.hisarr[index].id) {
                    this.$request.winery
                        .deleteWineryHistory({
                            wid: this.wineryData.id,
                            id: [this.wineryData.hisarr[index].id],
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("删除成功");
                                //判断是否需要删除已编辑过的数据
                                this.contentChangedKey.map((item, key) => {
                                    if (
                                        item == this.wineryData.hisarr[index].id
                                    ) {
                                        this.contentChangedKey.splice(key, 1);
                                    }
                                });
                                this.wineryData.hisarr.splice(index, 1);
                            }
                        });
                } else {
                    this.wineryData.hisarr.splice(index, 1);
                }
            });
        },
        removeHonarr(index) {
            this.$confirm("是否删除该条数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                //判断历史参数是否是新增的
                if (this.wineryData.honarr[index].id) {
                    this.$request.winery
                        .deleteWineryHonor({
                            wid: this.wineryData.id,
                            id: [this.wineryData.honarr[index].id],
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("删除成功");
                                //判断是否需要删除已编辑过的数据
                                this.honorContentChangedKey.map((item, key) => {
                                    if (
                                        item == this.wineryData.honarr[index].id
                                    ) {
                                        this.honorContentChangedKey.splice(
                                            key,
                                            1
                                        );
                                    }
                                });
                                this.wineryData.honarr.splice(index, 1);
                            }
                        });
                } else {
                    this.wineryData.honarr.splice(index, 1);
                }
            });
        },
        submit() {
            // return;
            this.$refs.wineryDataForm.validate((valid) => {
                if (valid) {
                    let wineryData = JSON.parse(
                        JSON.stringify(this.wineryData)
                    );
                    if (this.isEdit) {
                        if (this.removeAnnex.length > 0) {
                            this.$request.winery
                                .deleteWineryAnnex({
                                    id: this.removeAnnex,
                                    wid: wineryData.id,
                                })
                                .then((res) => {
                                    if (res.data.error_code != 0) {
                                        return false;
                                    }
                                });
                        }
                        if (this.addAnnex.length > 0) {
                            this.addAnnex.map((item) => {
                                this.$request.winery.addWineryAnnex({
                                    wid: wineryData.id,
                                    path: item.path,
                                });
                            });
                        }
                        wineryData.hisarr.map((item) => {
                            if (item.id == undefined) {
                                this.$request.winery.addWineryHistory({
                                    title: item.title,
                                    content: item.content,
                                    wid: wineryData.id,
                                });
                            } else if (
                                this.contentChangedKey.includes(item.id)
                            ) {
                                this.$request.winery
                                    .updateWineryHistory({
                                        wid: wineryData.id,
                                        id: item.id,
                                        title: item.title,
                                        content: item.content,
                                    })
                                    .then((res) => {
                                        if (res.data.error_code != 0) {
                                            this.$message.error(
                                                res.data.error_msg
                                            );
                                        }
                                    });
                            }
                        });
                        wineryData.honarr.map((item) => {
                            if (item.id == undefined) {
                                this.$request.winery.addWineryHonor({
                                    honordy_name: item.honordy_name,
                                    honordy_image: item.honordy_image.join(","),
                                    honordy_time: item.honordy_time,
                                    honordy_info: item.honordy_info,
                                    wid: wineryData.id,
                                });
                            } else if (
                                this.honorContentChangedKey.includes(item.id)
                            ) {
                                this.$request.winery
                                    .updateWineryHonor({
                                        wid: wineryData.id,
                                        id: item.id,
                                        honordy_name: item.honordy_name,
                                        honordy_time: item.honordy_time,
                                        honordy_image:
                                            item.honordy_image.join(","),
                                        honordy_info: item.honordy_info,
                                    })
                                    .then((res) => {
                                        console.warn(item, "item");
                                        if (res.data.error_code != 0) {
                                            this.$message.error(
                                                res.data.error_msg
                                            );
                                        }
                                    });
                            }
                        });
                    } else {
                        wineryData.annexarr = this.addAnnex;
                        wineryData.honarr.map((item) => {
                            item.honordy_image = item.honordy_image.join(",");
                        });
                    }

                    // if (this.fileList.length > 0) {
                    //   this.wineryData.image = this.fileList.join(",");
                    // }
                    let method = this.isEdit ? "updateWinery" : "addWinery";
                    // eslint-disable-next-line no-unreachable
                    this.$request.winery[method](wineryData).then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success(
                                (method ? "编辑" : "添加") + "成功"
                            );
                            this.$emit("updateSuccess");
                        }
                    });
                }
            });
        },
        editWinery(id) {
            this.isEdit = true;
            this.$request.winery.getWineryDetails({ id }).then((res) => {
                if (res.data.error_code == 0) {
                    for (const item in res.data.data) {
                        for (const item2 in this.wineryData) {
                            if (item == item2) {
                                this.wineryData[item] = res.data.data[item];
                            }
                        }
                    }
                    this.wineryData.id = res.data.data.id;
                    if (res.data.data.represent_grape_id) {
                        this.GrapeOptions = res.data.data.grapelist;
                        this.wineryData.represent_grape_id =
                            res.data.data.represent_grape_id
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                    }
                    if (res.data.data.represent_wine_id) {
                        this.WineOptions = res.data.data.Winec;
                        this.wineryData.represent_wine_id =
                            res.data.data.represent_wine_id
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                    }
                    if (res.data.data.classify_id) {
                        this.SuperiorOptions = res.data.data.superior;
                        this.wineryData.reparent = res.data.data.reparent
                            .split(",")
                            .map((item) => {
                                return Number(item);
                            });
                    }
                    if (res.data.data.represent_grape_id) {
                        this.wineryData.represent_grape_id =
                            res.data.data.represent_grape_id
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                    } else {
                        this.wineryData.represent_grape_id = [];
                    }
                    if (res.data.data.grade_id == 0) {
                        this.wineryData.grade_id = "";
                    }
                    if (res.data.data.classify_id == 0) {
                        this.wineryData.classify_id = "";
                    }
                    this.wineryData.hisarr = res.data.data.history.map(
                        (item) => {
                            console.warn(item);
                            return {
                                id: item.id,
                                title: item.title,
                                content: item.content,
                            };
                        }
                    );
                    this.wineryData.honarr = res.data.data.hon.map((item) => {
                        return {
                            id: item.id,
                            honordy_name: item.honordy_name,
                            honordy_image: item.honordy_image
                                ? item.honordy_image.split(",")
                                : [],
                            honordy_time: item.honordy_time,
                            honordy_info: item.honordy_info,
                        };
                    });
                    if (res.data.data.annex.length > 0) {
                        this.fileList = res.data.data.annex.map((item) => {
                            console.warn(
                                item.path,
                                "item.pathitem.pathitem.pathitem.pathitem.path"
                            );
                            return item.path;
                        });

                        this.wineryData.annexarr = res.data.data.annex.map(
                            (item) => {
                                return {
                                    id: item.id,
                                    wid: res.data.data.id,
                                    path: item.path,
                                };
                            }
                        );
                    }
                    // if (this.wineryData.image) {
                    //   this.fileList = [this.wineryData.image];
                    // }
                }
            });
        },

        changeContent(index) {
            if (!this.contentChangedKey.includes(index)) {
                this.contentChangedKey.push(index);
                return;
            }
        },
        changeHornorContent(index) {
            console.warn(this.honorContentChangedKey.includes(index));
            if (!this.honorContentChangedKey.includes(index)) {
                this.honorContentChangedKey.push(index);
                return;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.w-220 {
    width: 220px;
}
.wineryTitle {
    font-size: 18px;
    font-weight: 600;
}
.avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.amap-demo {
    height: 300px;
}
</style>
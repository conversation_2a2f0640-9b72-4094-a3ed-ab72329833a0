<template>
    <div>
        <el-card shadow="always">
            <el-input
                size="mini"
                class="m-r-10 w-normal"
                @keyup.enter.native="queryWinery"
                v-model="queryWineryData.keyword"
                placeholder="请输入酒庄"
                clearable
            ></el-input>
            <el-button type="warning" size="mini" @click="queryWinery"
                >查询</el-button
            >
            <el-button type="success" size="mini" @click="addWinery"
                >新增</el-button
            >
            <!-- <el-button
            type="danger"
            @click="removeWinery"
            :disabled="multipleSelection.length == 0"
            >删除</el-button
          > -->
        </el-card>
        <el-card shadow="always" style="margin-top: 20px">
            <el-table
                :data="wineryList"
                border
                size="mini"
                ref="multipleTable"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column label="编号" prop="id" width="100">
                </el-table-column>
                <el-table-column
                    label="酒庄中文名"
                    prop="winery_name_cn"
                    width="200"
                >
                </el-table-column>
                <el-table-column
                    label="酒庄英文名"
                    prop="winery_name_en"
                    width="200"
                >
                </el-table-column>
                <el-table-column label="完成度" prop="plan">
                    <template slot-scope="scope">
                        {{ scope.row.plan }}%
                    </template>
                </el-table-column>
                <el-table-column label="面积" prop="area" width="100">
                </el-table-column>
                <el-table-column label="拥有者" prop="owner" width="100">
                </el-table-column>
                <el-table-column label="地址" prop="addr" width="200">
                </el-table-column>
                <el-table-column label="网址" prop="web_url" width="200">
                </el-table-column>
                <!-- 没有 -->

                <el-table-column label="修改人" prop="member_name" width="100">
                </el-table-column>
                <el-table-column
                    label="修改时间"
                    width="180"
                    prop="update_time"
                >
                </el-table-column>
                <el-table-column
                    label="是否有附件"
                    prop="member_name"
                    width="80"
                >
                    <template slot-scope="scope">
                        {{ scope.row.annex.length > 0 ? "有" : "无" }}
                    </template>
                </el-table-column>
                <el-table-column label="酒款数" prop="wine_nums" width="80">
                </el-table-column>
                <el-table-column label="热搜" width="80" prop="is_hot">
                    <template slot-scope="scope">
                        <el-tag
                            @click="
                                handleStatus(
                                    scope.row.id,
                                    scope.row.is_hot,
                                    'is_hot',
                                    scope.row.winery_name_en
                                )
                            "
                            style="cursor: pointer"
                            :type="scope.row.is_hot == 1 ? 'success' : 'danger'"
                        >
                            {{ scope.row.is_hot == 1 ? "是" : "否" }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="首页" width="80" prop="is_index">
                    <template slot-scope="scope">
                        <el-tag
                            @click="
                                handleStatus(
                                    scope.row.id,
                                    scope.row.is_index,
                                    'is_index',
                                    scope.row.winery_name_en
                                )
                            "
                            style="cursor: pointer"
                            :type="
                                scope.row.is_index == 1 ? 'success' : 'danger'
                            "
                        >
                            {{ scope.row.is_index == 1 ? "是" : "否" }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="编辑状态" width="80" prop="is_finish">
                    <template slot-scope="scope">
                        <el-tag
                            @click="
                                handleStatus(
                                    scope.row.id,
                                    scope.row.is_finish,
                                    'is_finish',
                                    scope.row.winery_name_en
                                )
                            "
                            style="cursor: pointer"
                            :type="
                                scope.row.is_finish == 1 ? 'success' : 'danger'
                            "
                        >
                            {{ scope.row.is_finish == 1 ? "是" : "否" }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="120">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="editWinery(scope.row)"
                        >
                            编辑
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center; margin-top: 20px">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryWineryData.page"
                :page-size="queryWineryData.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            title="酒庄设置"
            :visible.sync="wineryVisible"
            width="80%"
            @close="closeDialog"
            :close-on-click-modal="false"
        >
            <updateWinery
                ref="updateWineryRef"
                v-if="wineryVisible"
                @updateSuccess="updateSuccess"
            ></updateWinery>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="wineryVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmUpdateWinery"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import updateWinery from "./updateWinery.vue";
export default {
    components: { updateWinery },
    data() {
        return {
            queryWineryData: {
                keyword: "",
                page: 1,
                limit: 10,
            },
            wineryList: [],
            total: 0,
            wineryVisible: false,
            multipleSelection: [],
        };
    },

    mounted() {
        this.getWineryList();
    },
    methods: {
        queryWinery() {
            this.queryWineryData.page = 1;
            this.getWineryList();
        },
        handleStatus(id, fileds, fileds_name, winery_name_en) {
            let params = {};
            if (fileds_name == "is_hot") {
                let is_hot = fileds == 1 ? 0 : 1;
                params = {
                    is_hot,
                    id,
                    winery_name_en,
                };
            } else if (fileds_name == "is_finish") {
                let is_finish = fileds == 1 ? 0 : 1;
                params = {
                    id,
                    is_finish,
                    winery_name_en,
                };
            } else if (fileds_name == "is_index") {
                let is_index = fileds == 1 ? 0 : 1;
                params = {
                    id,
                    is_index,
                    winery_name_en,
                };
            }
            this.$request.winery.updateWinery(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.$message.success("编辑成功");
                    this.getWineryList();
                }
            });
        },
        handleSizeChange(val) {
            this.queryWineryData.limit = val;
            this.queryWineryData.page = 1;
            this.getWineryList();
        },
        handleCurrentChange(val) {
            this.queryWineryData.page = val;
            this.getWineryList();
        },
        // changeHot(row) {
        //   this.$request.winery
        //     .updateWinery({
        //       id: row.id,
        //       is_hot: row.is_hot,
        //     })
        //     .then((response) => {
        //       if (response.data.code == 0) {
        //         this.$message.success("修改成功");
        //         this.getWineryList();
        //       }
        //     });
        // },
        modifyWinery(data) {
            this.$request.winery.updateWinery(data).then((response) => {
                if (response.data.error_code == 0) {
                    this.$message.success("修改成功");
                    this.getWineryList();
                }
            });
        },
        changeSort(row) {
            this.modifyWinery({
                id: row.id,
                winery_name_en: row.winery_name_en,
                sort: row.sort,
            });
        },
        getWineryList() {
            this.$request.winery
                .getWineryList(this.queryWineryData)
                .then((res) => {
                    this.wineryList = res.data.data.list;
                    this.total = res.data.data.total;
                });
        },
        addWinery() {
            this.wineryVisible = true;
        },
        comfirmUpdateWinery() {
            this.$nextTick(() => {
                this.$refs.updateWineryRef.submit();
            });
        },
        editWinery(row) {
            this.wineryVisible = true;
            this.$nextTick(() => {
                this.$refs.updateWineryRef.editWinery(row.id);
                console.warn(row.id);
            });
        },
        closeDialog() {
            this.getWineryList();
        },
        updateSuccess() {
            this.wineryVisible = false;
        },
        handleSelectionChange(val) {
            console.warn(val);
            this.multipleSelection = val;
        },
        removeWinery() {
            this.$confirm("是否删除数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                let ids = this.multipleSelection.map((item) => {
                    return item.id;
                });
                this.$request.winery
                    .deleteWinery({
                        id: ids,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("删除成功");
                            this.getWineryList();
                        }
                    });
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>

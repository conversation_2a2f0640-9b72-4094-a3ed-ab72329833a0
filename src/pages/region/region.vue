<template>
    <div>
        <el-card shadow="always">
            <el-input
                v-model="queryRegionData.keyword"
                size="mini"
                class="m-r-10 w-normal"
                @keyup.enter.native="queryRegion"
                placeholder="请输入产区"
                clearable
            ></el-input>
            <el-button type="warning" size="mini" @click="queryRegion"
                >查询</el-button
            >
            <el-button type="success" size="mini" @click="addRegion"
                >新增</el-button
            >
            <!-- <el-button
            type="danger"
            @click="removeRegion"
            :disabled="multipleSelection.length == 0"
            >删除</el-button
          > -->
        </el-card>
        <el-card shadow="always" style="margin-top: 20px">
            <el-table
                :data="regionList"
                border
                size="mini"
                ref="multipleTable"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column label="编号" prop="id" width="100">
                </el-table-column>
                <el-table-column label="中文名" prop="regions_name_cn">
                </el-table-column>
                <el-table-column label="英文名" prop="regions_name_en">
                </el-table-column>
                <el-table-column label="国家" prop="country" width="120">
                    <template slot-scope="scope">
                        {{
                            scope.row.country && scope.row.country.length > 0
                                ? scope.row.country[0].country_name_cn
                                : ""
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="上级产区id"
                    prop="parent_id"
                    width="100"
                >
                    <template slot-scope="scope">
                        {{
                            Boolean(scope.row.superior) &&
                            scope.row.superior.length > 0
                                ? scope.row.superior[0].regions_name_cn ||
                                  scope.row.superior[0].regions_name_en
                                : ""
                        }}
                    </template>
                </el-table-column>
                <el-table-column label="气候" prop="climate" width="200">
                </el-table-column>
                <!-- <el-table-column label="出口量" prop="capacity" width="100">
        </el-table-column> -->
                <!-- <el-table-column label="代表酒庄" prop="cpitacon" width="100">
        </el-table-column> -->
                <el-table-column label="代表葡萄" width="150">
                    <template slot-scope="scope">
                        {{
                            scope.row.grape != null &&
                            scope.row.grape.length > 0
                                ? scope.row.grape[0].gname_cn +
                                  scope.row.grape[0].gname_en
                                : ""
                        }}
                    </template>
                </el-table-column>
                <el-table-column label="完成度" width="90">
                    <template slot-scope="scope">
                        {{ scope.row.plan }}%
                    </template>
                </el-table-column>
                <el-table-column label="状态" prop="is_hot" width="80">
                    <template slot-scope="scope">
                        <el-tag
                            @click="
                                handleStatus(
                                    scope.row.id,
                                    scope.row.is_hot,
                                    'is_hot',
                                    scope.row.regions_name_en
                                )
                            "
                            style="cursor: pointer"
                            :type="scope.row.is_hot == 1 ? 'success' : 'danger'"
                        >
                            {{ scope.row.is_hot == 1 ? "是" : "否" }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="120">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="editRegion(scope.row)"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center; margin-top: 20px">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryRegionData.page"
                :page-size="queryRegionData.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            title="产区设置"
            :visible.sync="regionVisible"
            width="80%"
            @close="closeDialog"
            :close-on-click-modal="false"
        >
            <updateRegion
                ref="updateRegionRef"
                v-if="regionVisible"
                @updateSuccess="updateSuccess"
            ></updateRegion>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="regionVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmUpdateRegion"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import updateRegion from "./updateRegion.vue";
export default {
    components: { updateRegion },
    data() {
        return {
            queryRegionData: {
                keyword: "",
                page: 1,
                limit: 10,
            },
            regionList: [],
            total: 0,
            regionVisible: false,
            multipleSelection: [],
        };
    },

    mounted() {
        this.getRegionList();
    },

    methods: {
        queryRegion() {
            this.queryRegionData.page = 1;
            this.getRegionList();
        },
        handleStatus(id, fileds, fileds_name, country_name_en) {
            let params = {};
            if (fileds_name == "is_hot") {
                let is_hot = fileds == 1 ? 0 : 1;
                params = {
                    is_hot,
                    id,
                    country_name_en,
                };
            }
            this.$request.region.updateRegion(params).then((res) => {
                if (res.data.error_code == 0) {
                    this.$message.success("编辑成功");
                    this.getRegionList();
                }
            });
        },
        handleSizeChange(val) {
            this.queryRegionData.limit = val;
            this.queryRegionData.page = 1;
            this.getRegionList();
        },
        handleCurrentChange(val) {
            this.queryRegionData.page = val;
            this.getRegionList();
        },
        // changeHot(row) {
        //   this.$request.region
        //     .updateRegion({
        //       id: row.id,
        //       is_hot: row.is_hot,
        //     })
        //     .then((response) => {
        //       if (response.data.code == 0) {
        //         this.$message.success("修改成功");
        //         this.getRegionList();
        //       }
        //     });
        // },
        modifyRegion(data) {
            this.$request.region.updateRegion(data).then((response) => {
                if (response.data.error_code == 0) {
                    this.$message.success("修改成功");
                    this.getRegionList();
                }
            });
        },
        changeSort(row) {
            this.modifyRegion({
                id: row.id,
                region_name_en: row.region_name_en,
                sort: row.sort,
            });
        },
        getRegionList() {
            this.$request.region
                .getRegionList(this.queryRegionData)
                .then((res) => {
                    this.regionList = res.data.data.list;
                    this.total = res.data.data.total;
                });
        },
        addRegion() {
            this.regionVisible = true;
        },
        comfirmUpdateRegion() {
            this.$nextTick(() => {
                this.$refs.updateRegionRef.submit();
            });
        },
        editRegion(row) {
            this.regionVisible = true;
            this.$nextTick(() => {
                this.$refs.updateRegionRef.editRegion(row.id);
                console.warn(row.id);
            });
        },
        closeDialog() {
            this.getRegionList();
        },
        updateSuccess() {
            this.regionVisible = false;
        },
        handleSelectionChange(val) {
            console.warn(val);
            this.multipleSelection = val;
        },
        removeRegion() {
            this.$confirm("是否删除数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                let ids = this.multipleSelection.map((item) => {
                    return item.id;
                });
                this.$request.region
                    .deleteRegion({
                        id: ids,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("删除成功");
                            this.getRegionList();
                        }
                    });
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>

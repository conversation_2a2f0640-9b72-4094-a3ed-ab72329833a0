<template>
  <div>
    <div>
      <region-cascader-item
        style="display: flex"
        @openDialog="openRegion($event)"
        v-for="(item, index) in source"
        :key="index"
        :sourceItem="item"
      ></region-cascader-item>
    </div>
    <el-dialog title="" :visible.sync="regionVisible" width="80%">
      <updateRegion ref="updateRegion"></updateRegion>
      <span slot="footer" style="display: flex; justify-content: center">
        <el-button @click="regionVisible = false">Cancel</el-button>
        <el-button type="primary" @click="regionVisible = false">OK</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import regionCascaderItem from "./regionCascaderItem.vue";
import updateRegion from "./updateRegion.vue";
export default {
  components: {
    regionCascaderItem,
    updateRegion,
  },
  data() {
    return {
      regionVisible: false,
    };
  },

  mounted() {},

  methods: {
    openRegion(params) {
      console.warn(params);
      // if (params.plan) {
      //   this.regionVisible = true;
      // }
    },
  },
  props: {
    source: {
      type: Array,
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
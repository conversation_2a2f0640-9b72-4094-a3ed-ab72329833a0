<template>
    <div>
        <el-form
            :model="regionsData"
            ref="regionsDataForm"
            :rules="rules"
            label-width="100px"
            :inline="false"
            size="normal"
        >
            <el-card shadow="hover">
                <div slot="header">
                    <span class="regionsTitle">基础信息</span>
                </div>

                <el-form-item label="中文名" prop="regions_name_cn">
                    <el-input
                        v-model="regionsData.regions_name_cn"
                        placeholder="请输入中文名"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="英文名" prop="regions_name_en">
                    <el-input
                        v-model="regionsData.regions_name_en"
                        placeholder="请输入英文名"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="所属产区" prop="reparent">
                    <el-select
                        v-model="regionsData.reparent"
                        filterable
                        remote
                        clearable
                        reserve-keyword
                        placeholder="请输入产区"
                        :remote-method="getSuperiorList"
                        :loading="loading"
                        style="width: 280px"
                        @change="getRegionDetails"
                    >
                        <el-option
                            v-for="item in SuperiorOptions"
                            :key="item.id"
                            :label="item.regions_name_cn + item.regions_name_en"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="国家" prop="nation_id">
                    <el-select
                        v-model="regionsData.nation_id"
                        filterable
                        style="width: 280px"
                        remote
                        clearable
                        reserve-keyword
                        placeholder="请输入国家"
                        :remote-method="getCountryList"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in CountrySelectOptions"
                            :key="item.id"
                            :label="item.country_name_cn + item.country_name_en"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="产区图片" prop="image">
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="fileList"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
            </el-card>
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="regionsTitle">详细信息</span>
                </div>
                <el-form-item label="气候" prop="climate">
                    <el-input
                        v-model="regionsData.climate"
                        class="w-220"
                        placeholder="请输入气候"
                    ></el-input>
                </el-form-item>
                <el-form-item label="土壤" prop="soil">
                    <el-input
                        v-model="regionsData.soil"
                        class="w-220"
                        placeholder="请输入土壤"
                    ></el-input>
                </el-form-item>
                <el-form-item label="雨水" prop="rain">
                    <el-input
                        v-model="regionsData.rain"
                        class="w-220"
                        placeholder="请输入雨水"
                    ></el-input>
                </el-form-item>
                <el-form-item label="光照" prop="beam">
                    <el-input
                        v-model="regionsData.beam"
                        class="w-220"
                        placeholder="请输入光照"
                    ></el-input>
                </el-form-item>
                <el-form-item label="产区坐标">
                    <el-form-item label="经度" prop="long">
                        <el-input
                            v-model="regionsData.long"
                            class="w-220"
                            placeholder="请输入经度"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="纬度" prop="lat">
                        <el-input
                            v-model="regionsData.lat"
                            class="w-220"
                            placeholder="请输入纬度"
                        ></el-input>
                    </el-form-item>
                </el-form-item>
                <el-form-item label="葡萄园面积" prop="area">
                    <el-input
                        v-model="regionsData.area"
                        class="w-220"
                        placeholder="请输入葡萄园面积"
                    ></el-input>
                </el-form-item>
                <el-form-item label="收获季节" prop="harvest_season">
                    <el-input
                        v-model="regionsData.harvest_season"
                        class="w-220"
                        placeholder="请输入收获季节"
                    ></el-input>
                </el-form-item>
                <el-form-item label="代表葡萄" prop="represent_grape_id">
                    <el-select
                        v-model="regionsData.represent_grape_id"
                        filterable
                        remote
                        style="width: 280px"
                        multiple
                        reserve-keyword
                        placeholder="请输入葡萄品种"
                        :remote-method="getGrapeList"
                    >
                        <el-option
                            v-for="item in GrapeOptions"
                            :key="item.id"
                            :label="item.gname_cn + item.gname_en"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="代表酒款" prop="represent_wine_id">
                    <el-select
                        v-model="regionsData.represent_wine_id"
                        filterable
                        remote
                        clearable
                        multiple
                        style="width: 280px"
                        reserve-keyword
                        placeholder="请输入酒款"
                        :remote-method="getWineList"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in WineOptions"
                            :key="item.id"
                            :label="item.winename + item.wename"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-card>
            <!-- <el-card shadow="hover" style="margin-top: 20px">
        <div slot="header">
          <span class="regionsTitle">评价</span>
        </div>
        <el-form-item label-width="40px" prop="evaluate">
          <el-input
            rows="5"
            type="textarea"
            v-model="regionsData.evaluate"
          ></el-input>
        </el-form-item>
      </el-card> -->
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="regionsTitle">优秀年份</span>
                </div>
                <el-form-item label-width="40px">
                    <el-button
                        type="primary"
                        size="default"
                        @click="addHisparam"
                        >新增</el-button
                    >
                </el-form-item>
                <el-form-item
                    label-width="40px"
                    v-for="(item, key) in regionsData.hisparam"
                    :key="key"
                    :prop="'hisparam.' + key + '.title'"
                    :rules="{
                        required: true,
                        message: '请输入标题',
                        trigger: 'blur',
                    }"
                >
                    <el-input
                        v-model="item.title"
                        class="w-220"
                        @input="changeContent(item.id)"
                        placeholder="请输入年份"
                    ></el-input>
                    <el-input
                        v-model="item.white_grade_name"
                        style="max-width: 220px; margin: 0 20px"
                        placeholder="请输入白葡萄"
                        @input="changeContent(item.id)"
                    ></el-input>
                    <el-input
                        v-model="item.red_grade_name"
                        style="max-width: 220px; margin: 0 20px"
                        placeholder="请输入红葡萄"
                        @input="changeContent(item.id)"
                    ></el-input>
                    <el-button
                        type="danger"
                        size="default"
                        @click="removeHisparam(key)"
                        >删除</el-button
                    >
                </el-form-item>
            </el-card>
            <!-- <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="regionsTitle">概述资料</span>
                </div>
                <el-form-item label-width="40px" prop="summary">
                    <el-input
                        rows="5"
                        type="textarea"
                        v-model="regionsData.summary"
                    ></el-input>
                </el-form-item>
            </el-card> -->
            <el-card shadow="hover" style="margin-top: 20px">
                <div slot="header">
                    <span class="regionsTitle">网页概述</span>
                </div>
                <el-form-item prop="summary_web" label-width="0">
                    <Tinymce
                        ref="editor"
                        v-model.trim="regionsData.summary_web"
                        :height="300"
                    />
                </el-form-item>
            </el-card>
            <el-form-item
                label="资料完成"
                prop="is_finish"
                style="margin-top: 20px"
            >
                <el-radio-group v-model="regionsData.is_finish">
                    <el-radio
                        v-for="item in is_finish"
                        :label="item.value"
                        :key="item.value"
                    >
                        {{ item.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import Tinymce from "../../components/Tinymce/index";
import VosOss from "vos-oss";
// soil Map from "../../components/Map.vue";
export default {
    components: {
        Tinymce,
        VosOss,
        // Map,
    },
    data() {
        return {
            regionsData: {
                regions_name_cn: "",
                regions_name_en: "",
                represent_grape_id: [],
                reparent: "",
                nation_id: "",
                represent_wine_id: [],
                continent_id: "",
                is_finish: "",
                long: "",
                lat: "",
                image: "",
                sort: "",
                climate: "",
                rain: "",
                soil: "",
                beam: "",
                area: "",
                summary: "",
                summary_web: "",
                harvest_season: "",
                hisparam: [],
            },
            rules: {
                regions_name_en: [
                    {
                        required: true,
                        message: "请输入英文名称",
                        trigger: "blur",
                    },
                ],
                // title:[
                //   { required: true, message: "请输入英文名称", trigger: "blur" },
                // ]
            },
            GrapeOptions: [],
            SuperiorOptions: [], //产区列表
            WineOptions: [], //产区列表
            mainlandOptions: [],
            CountryOptions: [], //国家列表
            is_finish: [
                {
                    label: "已完成",
                    value: 1,
                },
                {
                    label: "未完成",
                    value: 0,
                },
            ],
            dir: "vinehoo/wiki/region/",
            fileList: [],
            loading: false,
            isEdit: false,
            contentChangedKey: [],
            selectedParent: {},
            reparent: "",
            CountrySelectOptions: [], //国家下拉框列表
        };
    },
    mounted() {
        this.getContparam();
    },
    methods: {
        changeParent(keyword) {
            this.getSuperiorList(keyword, true);
        },
        getContparam() {
            this.$request.region.getContparam().then((res) => {
                if (res.data.error_code == 0) {
                    this.CountryOptions = res.data.data.country;
                }
            });
        },
        getRegionDetails(id) {
            if (id) {
                this.$request.region
                    .getRegionDetails({
                        id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.reparent =
                                res.data.data.reparent + "," + res.data.data.id;
                        }
                    });
            } else {
                this.reparent = "";
            }
        },
        getSuperiorList(keyword) {
            if (keyword) {
                if (this.regionsData.reparent) {
                    this.regionsData.reparent = parseInt(
                        this.regionsData.reparent
                    );
                }
                this.$request.product
                    .getRegionList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.SuperiorOptions = res.data.data.list;
                        }
                    });
            }
        },
        //国家
        getCountryList(keyword) {
            if (keyword) {
                // if (this.ruleForm.country_id) {
                //     this.ruleForm.country_id = parseInt(
                //         this.ruleForm.country_id
                //     );
                // }
                this.$request.product
                    .getCountryList({
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.CountrySelectOptions = res.data.data.list;
                        }
                    });
            }
        },
        getGrapeList(keyword) {
            if (keyword) {
                this.$request.product
                    .getGrapeList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.GrapeOptions = res.data.data.list;
                        }
                    });
            }
        },
        getWineList(keyword) {
            if (keyword) {
                this.$request.region
                    .getWineList({
                        keyword: keyword,
                        fields: "id,winename,wename",
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.WineOptions = res.data.data.list;
                        }
                    });
            }
        },
        addHisparam() {
            this.regionsData.hisparam.push({
                title: "",
                red_grade_name: "",
                white_grade_name: "",
            });
        },
        removeHisparam(index) {
            this.$confirm("是否删除该条数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                //判断历史参数是否是新增的
                if (this.regionsData.hisparam[index].id) {
                    this.$request.region
                        .deleteRegionHistory({
                            regions_id: this.regionsData.id,
                            id: this.regionsData.hisparam[index].id,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("删除成功");
                                //判断是否需要删除已编辑过的数据
                                this.contentChangedKey.map((item, key) => {
                                    if (
                                        item ==
                                        this.regionsData.hisparam[index].id
                                    ) {
                                        this.contentChangedKey.splice(key, 1);
                                    }
                                });
                                this.regionsData.hisparam.splice(index, 1);
                            }
                        });
                } else {
                    this.regionsData.hisparam.splice(index, 1);
                }
            });
        },
        submit() {
            this.$refs.regionsDataForm.validate((valid) => {
                if (valid) {
                    if (this.isEdit) {
                        this.regionsData.hisparam.map((item) => {
                            if (item.id == undefined) {
                                this.$request.region.addRegionHistory({
                                    title: item.title,
                                    red_grade_name: item.red_grade_name,
                                    white_grade_name: item.white_grade_name,
                                    regions_id: this.regionsData.id,
                                });
                            } else if (
                                this.contentChangedKey.includes(item.id)
                            ) {
                                this.$request.region
                                    .updateRegionHistory({
                                        regions_id: this.regionsData.id,
                                        id: item.id,
                                        title: item.title,
                                        red_grade_name: item.red_grade_name,
                                        white_grade_name: item.white_grade_name,
                                    })
                                    .then((res) => {
                                        if (res.data.error_code != 0) {
                                            this.$message.error(
                                                res.data.error_msg
                                            );
                                        }
                                    });
                            }
                        });
                    }
                    let data = JSON.parse(JSON.stringify(this.regionsData));
                    if (this.reparent) {
                        let reparent = this.reparent;
                        data.reparent = reparent.split(",");
                    } else {
                        data.reparent = [];
                    }
                    data.image = this.fileList.join(",");
                    let method = this.isEdit ? "updateRegion" : "addRegion";
                    this.$request.region[method](data).then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success(
                                (method ? "编辑" : "添加") + "成功"
                            );
                            this.$emit("updateSuccess");
                        }
                    });
                }
            });
        },
        editRegion(id) {
            this.isEdit = true;
            this.$request.region.getRegionDetails({ id }).then((res) => {
                if (res.data.error_code == 0) {
                    const { country: $country } = res?.data?.data || {};
                    for (const item in res.data.data) {
                        for (const item2 in this.regionsData) {
                            if (item == item2) {
                                this.regionsData[item] = res.data.data[item];
                            }
                        }
                    }
                    this.regionsData.id = res.data.data.id;
                    if (res.data.data.represent_grape_id) {
                        this.GrapeOptions = res.data.data.grape;
                        this.regionsData.represent_grape_id =
                            res.data.data.represent_grape_id
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                    } else {
                        this.regionsData.represent_grape_id = [];
                    }
                    if (res.data.data.represent_wine_id) {
                        this.WineOptions = res.data.data.Winec;
                        this.regionsData.represent_wine_id =
                            res.data.data.represent_wine_id
                                .split(",")
                                .map((item) => {
                                    return Number(item);
                                });
                    } else {
                        this.regionsData.represent_wine_id = [];
                    }
                    if (res.data.data.reparent) {
                        this.SuperiorOptions = res.data.data.superior;
                        // let reparent = res.data.data.reparent.split(",");
                        // this.regionsData.reparent = Number(
                        //     reparent[reparent.length - 1]
                        // );
                        this.regionsData.reparent = res.data.data.parent_id;
                        // this.getSuperiorList(this.regionsData.reparent, true);
                        this.reparent = res.data.data.reparent;
                    }

                    if ($country?.length) {
                        this.CountrySelectOptions = $country;
                        this.regionsData.nation_id = $country[0].id;
                    }

                    this.regionsData.hisparam = res.data.data.his.map(
                        (item) => {
                            return {
                                id: item.id,
                                title: item.title,
                                red_grade_name: item.red_grade_name,
                                white_grade_name: item.white_grade_name,
                            };
                        }
                    );
                    if (this.regionsData.image) {
                        this.fileList = [this.regionsData.image];
                    } else {
                        this.fileList = [];
                    }
                }
            });
        },
        changeContent(index) {
            if (!this.contentChangedKey.includes(index)) {
                this.contentChangedKey.push(index);
                return;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.w-220 {
    width: 220px;
}
.regionsTitle {
    font-size: 18px;
    font-weight: 600;
}
.avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.amap-demo {
    height: 300px;
}
</style>

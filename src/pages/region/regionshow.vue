<template>
    <div>
        <el-card shadow="hover">
            <el-form :model="regionMapQuery" :inline="true" size="mini">
                <el-form-item label="">
                    <el-select
                        v-model="regionMapQuery.id"
                        placeholder="请选择国家"
                        clearable
                        @change="queryRegionMap"
                    >
                        <el-option
                            v-for="item in countryList"
                            :key="item.id"
                            :label="
                                item.country_name_cn || item.country_name_en
                            "
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item>
                    <el-button
                        type="primary"
                        :disabled="!regionMapQuery.id"
                        @click="queryRegionMap"
                        >查询</el-button
                    >
                </el-form-item> -->
            </el-form>
        </el-card>

        <el-card
            shadow="hover"
            style="
                overflow: scroll;
                max-height: 76vh;
                width: 100%;
                margin-top: 20px;
            "
        >
            <regionCascader
                v-if="regionMapQuery.id"
                :source="regionMapList"
            ></regionCascader>
            <el-empty v-else description="请选择国家"> </el-empty>
        </el-card>
    </div>
</template>

<script>
import regionCascader from "./regionCascader.vue";
export default {
    components: {
        regionCascader,
    },
    data() {
        return {
            regionMapQuery: {
                id: "",
            },
            regionMapList: [],
            regionMapListClone: [],
            countryList: [],
        };
    },

    mounted() {
        // this.getRegionMap();
        this.getCountryList();
    },

    methods: {
        getCountryList() {
            this.$request.country
                .getCountryList({
                    page: 1,
                    limit: 1000,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.countryList = res.data.data.list;
                    }
                });
        },
        // changeRegion(item) {
        //     this.regionMapQuery.id = item;
        // },
        queryRegionMap() {
            if (this.regionMapQuery.id == "") {
                return;
            }
            // if (this.regionMapQuery.id) {
            //   let regionMapList = [];
            //   this.regionMapListClone.map((item) => {
            //     if (item.id == this.regionMapQuery.id) {
            //       regionMapList = [item];
            //     }
            //   });
            //   this.regionMapList = JSON.parse(JSON.stringify(regionMapList));
            // }else{
            //   this.regionMapList = JSON.parse(JSON.stringify(this.regionMapListClone));
            // }
            this.getRegionMap();
        },
        getRegionMap() {
            this.$request.region
                .getRegionMap({
                    id: this.regionMapQuery.id,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.regionMapList = res.data.data;
                        this.regionMapListClone = res.data.data;
                    }
                });
        },
    },
};
</script>

<style lang="scss" scoped></style>

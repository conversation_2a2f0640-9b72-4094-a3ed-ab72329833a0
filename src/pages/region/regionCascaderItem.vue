<template>
  <div class="source-item_container">
    <div class="source-item_content">
      <div
        class="source-item_parent"
        style="display: flex; align-items: center"
      >
        <el-tag
          style="cursor: pointer"
          @click="openRegion(sourceItem.id, sourceItem.plan)"
        >
          {{ sourceItem.regions_name_cn }} {{ sourceItem.regions_name_en }}
        </el-tag>
        <span
          class="source-item_plan"
          style="color: red; margin-left: 5px; font-weight: 500"
          v-if="sourceItem.plan"
          >{{ sourceItem.plan }}%</span
        >
        <span style="font-weight: 500; margin-left: 5px"
          >({{ sourceItem.id }})</span
        >
      </div>

      <div class="source-item_childer">
        <div v-for="item in sourceItem.child" :key="item.id">
          <cascader-item v-if="sourceItem.child" :sourceItem="item">
          </cascader-item>
        </div>
      </div>
    </div>
    <el-dialog title="" :visible.sync="regionVisible" width="80%">
      <updateRegion
        ref="updateRegion"
        v-if="regionVisible"
        @updateSuccess="finishModify"
      ></updateRegion>
      <span slot="footer" style="display: flex; justify-content: center">
        <el-button @click="regionVisible = false">取消</el-button>
        <el-button type="primary" @click="comfirmRegion">确认</el-button>
      </span>
    </el-dialog>
    <!-- countryVisible -->
    <el-dialog title="" :visible.sync="countryVisible" width="80%">
      <updateCountry
        ref="updateCountry"
        v-if="countryVisible"
        @updateSuccess="finishModify"
      ></updateCountry>
      <span slot="footer" style="display: flex; justify-content: center">
        <el-button @click="countryVisible = false">取消</el-button>
        <el-button type="primary" @click="comfirmCountry">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import cascaderItem from "./regionCascaderItem.vue";
import updateRegion from "./updateRegion.vue";
import updateCountry from "../country/updateCountry.vue";
export default {
  components: {
    updateRegion,
    updateCountry,
    // cascaderItem,
  },
  name: "cascaderItem",
  props: {
    sourceItem: {
      type: Object,
    },
  },

  data() {
    return {
      regionVisible: false,
      countryVisible: false,
    };
  },

  mounted() {},

  methods: {
    finishModify() {
      this.regionVisible = false;
      this.countryVisible = false;
    },
    // eslint-disable-next-line no-unused-vars
    openRegion(id, plan) {
      if (plan) {
        this.regionVisible = true;
        this.$nextTick(() => {
          this.$refs.updateRegion.editRegion(id);
        });
      } else {
        this.countryVisible = true;
        this.$nextTick(() => {
          this.$refs.updateCountry.editCountry(id);
        });
      }
    },

    comfirmRegion() {
      this.$refs.updateRegion.submit();
    },
    comfirmCountry() {
      this.$refs.updateCountry.submit();
    },
  },
};
</script>

<style lang="scss" scoped>
@keyframes shadow-drop-center {
  0% {
    box-shadow: 0 0 0 0 transparent;
  }
  100% {
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.08);
  }
}
.source-item_container {
  display: flex;
  animation: shadow-drop-center 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) reverse both;
  > :hover {
    animation: shadow-drop-center 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      normal both;
  }
  .source-item_content {
    display: flex;
    margin-top: 10px;
    margin-left: 10px;
    border: 1px solid #ebeef5;
    padding: 20px;
    border-radius: 4px;
    // width: 100%;

    .source-item_parent {
      .source-item_plan {
        color: red;
      }
    }
    .source-item_childer {
    }
  }
}
</style>
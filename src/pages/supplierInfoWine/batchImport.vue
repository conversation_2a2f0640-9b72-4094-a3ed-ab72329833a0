<template>
    <div>
        <el-dialog
            title="批量导入"
            :visible="visible"
            width="50%"
            :before-close="closeDialog"
            :close-on-click-modal="false"
        >
            <el-select
                v-model="query.product_category"
                placeholder="品类"
                size="mini"
                class="m-r-10"
            >
                <el-option
                    v-for="item in productCategoryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <!-- <el-select
                v-model="query.supplier_id"
                placeholder="供应商"
                size="mini"
                class="m-r-10"
            >
                <el-option
                    v-for="item in supplierListOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
            </el-select> -->
            <!-- multiple -->
            <el-select
                v-model="query.supplier_id"
                filterable
                remote
                reserve-keyword
                placeholder="供应商"
                :remote-method="remoteMethod"
                :loading="loading"
                size="mini"
                class="m-r-10"
                clearable
            >
                <el-option
                    v-for="item in supplierListOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
            </el-select>
            <el-date-picker
                v-model="query.quotation_time"
                type="date"
                align="right"
                size="mini"
                value-format="yyyy-MM-dd"
                placeholder="选择报价时间"
                class="m-r-10"
                clearable
            >
            </el-date-picker>

            <div class="m-t-20">
                <vos-oss
                    @on-success="handleSuccess"
                    ref="vos"
                    filesType="/"
                    listType="text"
                    :showFileList="false"
                    :dir="dir"
                    :file-list="file_list"
                    :limit="1"
                    :disabled="
                        !(
                            this.query.product_category &&
                            this.query.supplier_id &&
                            this.query.quotation_time
                        )
                    "
                >
                    <el-button
                        size="mini"
                        type="success"
                        :disabled="
                            !(
                                this.query.product_category &&
                                this.query.supplier_id &&
                                this.query.quotation_time
                            )
                        "
                        >批量导入</el-button
                    >
                </vos-oss>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import vosOss from "vos-oss";

import dialogmixin from "../../mixins/dialogmixin";
export default {
    components: {
        vosOss,
    },
    mixins: [dialogmixin],
    data() {
        return {
            dir: "vinehoo/wiki/supplier/",
            file_list: [],
            time: "",
            query: {
                product_category: "",
                supplier_id: "",
                quotation_time: "",
            },
            productCategoryOptions: [],
            supplierListOptions: [],
            loading: false,
        };
    },
    watch: {
        visible(val) {
            if (val) {
                this.query = this.$options.data().query;
                this.supplierListOptions = [];
                this.productCategoryOptions = [];
                this.file_list = [];
                this.$nextTick(() => {
                    console.log(this.$refs.vos);
                    this.$refs.vos.handleviewFileList([]);
                });
                this.productCategory();
                // this.supplierList();
            }
        },
    },
    methods: {
        //供应商产品类别
        async productCategory() {
            let res = await this.$request.supplier.productCategory();
            if (res.data.error_code == 0) {
                this.productCategoryOptions = res.data.data.filter(
                    (item) => item.value != 13
                );
            }
        },
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                //  supplierList
                this.$request.supplier
                    .allListSupplier({ page: 1, limit: 50, name: query })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.supplierListOptions = res.data.data.list;
                        }
                        this.loading = false;
                    });
            } else {
                this.supplierListOptions = [];
            }
        },
        // //供应商列表
        // async supplierList() {
        //     let res = await this.$request.supplier.supplierList();
        //     if (res.data.error_code == 0) {
        //         this.supplierListOptions = res.data.data.list;
        //     }
        // },
        async handleSuccess(file) {
            console.log("批量导入", file);
            if (
                !(
                    this.query.product_category &&
                    this.query.supplier_id &&
                    this.query.quotation_time
                )
            ) {
                this.$message.warning("请选择品类、供应商、报价时间");
                return;
            }
            let data = {
                ...this.query,
                file: file.file,
            };
            data.supplier = this.supplierListOptions.find(
                (item) => item.id == this.query.supplier_id
            ).name;
            let res = await this.$request.supplier.supplierproductImpport(data);
            if (res.data.error_code == 0) {
                this.$message.success(`${res.data.error_msg}`);
                this.closeDialog();
            }
            this.file_list = [];
            this.$refs.vos.handleviewFileList([]);
        },
    },
};
</script>

<style></style>

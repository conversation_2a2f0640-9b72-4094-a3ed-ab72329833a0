<template>
    <div>
        <el-dialog
            title="新增供应商商品信息"
            :visible="visible"
            width="65%"
            :before-close="closeDialog"
            :close-on-click-modal="false"
        >
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                label-width="130px"
                class="demo-ruleForm"
                size="mini"
            >
                <div class="f_box">
                    <div>
                        <el-form-item label="英文品名" prop="en_product_name">
                            <el-input
                                v-model="ruleForm.en_product_name"
                                class="w-large"
                                placeholder="请输入英文品名"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="规格" prop="specs">
                            <el-input
                                v-model="ruleForm.specs"
                                placeholder="请输入规格"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="国家（中文）" prop="cn_country">
                            <el-input
                                v-model="ruleForm.cn_country"
                                placeholder="请输入国家（中文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            label="产区（中文）"
                            prop="producing_area_cn"
                        >
                            <el-input
                                v-model="ruleForm.producing_area_cn"
                                placeholder="请输入产区（中文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>

                        <el-form-item label="单位" prop="pack">
                            <el-select
                                v-model="ruleForm.pack"
                                filterable
                                placeholder="请选择单位"
                            >
                                <el-option
                                    v-for="item in PackWineOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="供应商" prop="supplier_id">
                            <!-- <el-select
                                v-model="ruleForm.supplier_id"
                                filterable
                                placeholder="请选择供应商"
                            >
                                <el-option
                                    v-for="item in supplierListOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select> -->
                            <el-select
                                v-model="ruleForm.supplier_id"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入供应商"
                                :remote-method="remoteMethod"
                                :loading="loading"
                                size="mini"
                                class="m-r-10"
                                clearable
                            >
                                <el-option
                                    v-for="item in supplierListOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="报价时间" prop="quotation_time">
                            <el-date-picker
                                v-model="ruleForm.quotation_time"
                                type="date"
                                align="right"
                                size="mini"
                                value-format="yyyy-MM-dd"
                                placeholder="选择报价时间"
                                class="m-r-10"
                                style="width: 100%"
                            >
                            </el-date-picker>
                        </el-form-item>

                        <!-- 葡萄酒 -->
                        <div v-if="type == 1">
                            <el-form-item label="起泡状态" prop="bubble_state">
                                <el-select
                                    v-model="ruleForm.bubble_state"
                                    placeholder="请选择"
                                    @change="
                                        bubbleStateChange(ruleForm.bubble_state)
                                    "
                                >
                                    <el-option
                                        v-for="item in FoamingOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="颜色" prop="color">
                                <el-select
                                    v-model="ruleForm.color"
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="item in ColorOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <!-- 啤酒 -->
                        <div v-if="type == 2">
                            <el-form-item label="酒款风格" prop="wine_style">
                                <el-input
                                    v-model="ruleForm.wine_style"
                                    placeholder="请输入酒款风格"
                                ></el-input>
                            </el-form-item>
                        </div>
                        <!-- 啤酒、清酒 -->
                        <div v-if="[2, 4].includes(type)">
                            <el-form-item label="罐装日期" prop="bottled_year">
                                <!-- <el-date-picker
                                    v-model="ruleForm.bottled_year"
                                    type="datetime"
                                    align="right"
                                    size="mini"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    placeholder="选择罐装日期"
                                    class="m-r-10"
                                    style="width: 100%"
                                >
                                </el-date-picker> -->
                                <el-date-picker
                                    v-model="ruleForm.bottled_year"
                                    size="mini"
                                    value-format="yyyy-MM-dd"
                                    type="date"
                                    class="m-r-10"
                                    style="width: 100%"
                                    placeholder="选择罐装日期"
                                    :picker-options="pickerOptions"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <!-- 果酒 -->
                        <div v-if="type == 3">
                            <el-form-item label="水果" prop="fruit">
                                <el-input
                                    v-model="ruleForm.fruit"
                                    class="w-large"
                                    placeholder="请输入水果"
                                ></el-input>
                            </el-form-item>
                        </div>
                        <!-- 清酒 -->
                        <div v-if="type == 4">
                            <el-form-item
                                label="精米步和"
                                prop="jingmi_stepping"
                            >
                                <el-input
                                    v-model="ruleForm.jingmi_stepping"
                                    placeholder="请输入精米步和"
                                ></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div>
                        <el-form-item label="中文品名" prop="cn_product_name">
                            <el-input
                                v-model="ruleForm.cn_product_name"
                                placeholder="请输入中文品名"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="供应商报价" prop="price">
                            <el-input
                                v-model="ruleForm.price"
                                placeholder="请输入供应商报价"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="国家（英文）" prop="en_country">
                            <el-input
                                v-model="ruleForm.en_country"
                                placeholder="请输入国家（英文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            label="产区（英文）"
                            prop="producing_area_en"
                        >
                            <el-input
                                v-model="ruleForm.producing_area_en"
                                placeholder="请输入产区（英文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="进口类型" prop="import_type">
                            <el-select
                                v-model="ruleForm.import_type"
                                filterable
                                placeholder="请选择进口类型"
                            >
                                <el-option
                                    v-for="item in ImportTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <!-- 葡萄酒 -->
                        <div v-if="type == 1">
                            <el-form-item label="供应商库存" prop="stock">
                                <!-- <el-input
                                    v-model="ruleForm.stock"
                                    class="w-large"
                                    placeholder="请输入供应商库存"
                                ></el-input> -->
                                <el-input-number
                                    v-model="ruleForm.stock"
                                    :controls="false"
                                    :precision="0"
                                    class="w-normal"
                                    placeholder="请输入供应商库存"
                                ></el-input-number>
                            </el-form-item>

                            <el-form-item
                                label="葡萄酒类型"
                                prop="product_type"
                            >
                                <el-select
                                    v-model="ruleForm.product_type"
                                    filterable
                                    placeholder="请选择葡萄酒类型"
                                >
                                    <el-option
                                        v-for="item in typeList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="甜度" prop="sweetness">
                                <el-select
                                    v-model="ruleForm.sweetness"
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="item in sweetOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <!-- 葡萄酒、白兰地 -->
                        <div v-if="[1, 6].includes(type)">
                            <el-form-item label="葡萄采摘年份" prop="year">
                                <el-date-picker
                                    v-model="ruleForm.year"
                                    type="year"
                                    value-format="yyyy"
                                    class="m-r-10"
                                    style="width: 100%"
                                    placeholder="请选择葡萄采摘年份"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <!-- 啤酒 -->
                        <div v-if="type == 2">
                            <el-form-item label="苦度" prop="bitter_extent">
                                <el-input
                                    v-model="ruleForm.bitter_extent"
                                    placeholder="请输入苦度"
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="原麦汁浓度"
                                prop="original_gravity"
                            >
                                <el-input
                                    v-model="ruleForm.original_gravity"
                                    class="w-large"
                                    placeholder="请输入原麦汁浓度"
                                ></el-input>
                            </el-form-item>
                        </div>
                        <!-- 清酒 -->
                        <div v-if="type == 4">
                            <el-form-item label="生产日期" prop="produce_time">
                                <el-date-picker
                                    v-model="ruleForm.produce_time"
                                    type="date"
                                    align="right"
                                    size="mini"
                                    value-format="yyyy-MM-dd"
                                    placeholder="请选择生产日期"
                                    class="m-r-10"
                                    style="width: 100%"
                                    :picker-options="pickerOptions"
                                >
                                </el-date-picker>
                                <!-- <el-date-picker
                                    v-model="ruleForm.produce_time"
                                    type="datetime"
                                    align="right"
                                    size="mini"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    placeholder="请选择生产日期"
                                    class="m-r-10"
                                    style="width: 100%"
                                >
                                </el-date-picker> -->
                            </el-form-item>
                            <el-form-item label="米种" prop="rice_seed">
                                <el-input
                                    v-model="ruleForm.rice_seed"
                                    placeholder="请输入米种"
                                ></el-input>
                            </el-form-item>
                        </div>
                    </div>
                </div>

                <el-form-item style="text-align: center; margin-left: -130px">
                    <el-button @click="closeDialog">取消</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm')"
                        >确定</el-button
                    >
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import dialogmixin from "../../mixins/dialogmixin";
import {
    ColorOptions,
    FoamingOptions,
    IntensifyTypeOptions,
    BoxGaugePackageOptions,
    SweetStandOptions,
    SweetFoamingOptions,
    PackWineOptions,
    ImportTypeOptions,
} from "../../tools/mapper";
export default {
    props: ["type"],
    mixins: [dialogmixin],
    data() {
        return {
            pickerOptions: {
                //返回true不可选，返回false可选
                disabledDate: (time) => {
                    return time.getTime() > Date.now();
                },
            },
            ColorOptions, //颜色
            FoamingOptions,
            IntensifyTypeOptions,
            BoxGaugePackageOptions,
            SweetStandOptions,
            SweetFoamingOptions,
            PackWineOptions,
            ImportTypeOptions,
            sweetOptions: [],
            typeList: [],
            supplierListOptions: [],
            loading: false,
            ruleForm: {
                en_product_name: "",
                cn_product_name: "",
                cn_country: "",
                color: "",
                price: "",
                en_country: "",
                specs: "",
                producing_area_en: "",
                year: "",
                bubble_state: "",
                supplier_id: "",
                producing_area_cn: "",
                quotation_time: "",
                stock: "",
                sweetness: "",
                product_type: "",
                bottled_year: "",
                wine_style: "",
                original_gravity: "",
                bitter_extent: "",
                jingmi_stepping: "",
                produce_time: "",
                rice_seed: "",
                import_type: "",
                pack: "",
            },
            rules: {
                quotation_time: [
                    {
                        required: true,
                        message: "请选择报价时间",
                        trigger: "blur",
                    },
                ],
                import_type: [
                    {
                        required: true,
                        message: "请选择进口类型",
                        trigger: "change",
                    },
                ],
                pack: [
                    {
                        required: true,
                        message: "请选择单位",
                        trigger: "change",
                    },
                ],
                supplier_id: [
                    {
                        required: true,
                        message: "请选择供应商",
                        trigger: "change",
                    },
                ],
                cn_product_name: [
                    {
                        required: true,
                        message: "请输入中文品名",
                        trigger: "blur",
                    },
                ],
                en_product_name: [
                    {
                        required: true,
                        message: "请输入英文品名",
                        trigger: "blur",
                    },
                ],
                specs: [
                    {
                        required: true,
                        message: "请输入规格",
                        trigger: "blur",
                    },
                ],
                price: [
                    {
                        required: true,
                        message: "请输入供应商报价",
                        trigger: "blur",
                    },
                ],
                cn_country: [
                    {
                        required: true,
                        message: "请输入国家中文",
                        trigger: "blur",
                    },
                ],
                en_country: [
                    {
                        required: true,
                        message: "请输入国家英文",
                        trigger: "blur",
                    },
                ],
                producing_area_cn: [
                    {
                        required: true,
                        message: "请输入产区中文",
                        trigger: "blur",
                    },
                ],
                producing_area_en: [
                    {
                        required: true,
                        message: "请输入产区英文",
                        trigger: "blur",
                    },
                ],
                year: [
                    {
                        required: true,
                        message: "请选择葡萄采摘年份",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    watch: {
        visible(val) {
            if (val) {
                this.ruleForm = this.$options.data().ruleForm;
                this.$nextTick(() => {
                    this.$refs.ruleForm.resetFields();
                });
                // this.supplierList();
                this.productCategory();
                this.getProductCategory();
            }
        },
    },
    methods: {
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                //  supplierList
                this.$request.supplier
                    .allListSupplier({ page: 1, limit: 50, name: query })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.supplierListOptions = res.data.data.list;
                        }
                        this.loading = false;
                    });
            } else {
                this.supplierListOptions = [];
            }
        },
        //供应商列表
        async supplierList() {
            // supplierList;
            let res = await this.$request.supplier.allListSupplier();
            if (res.data.error_code == 0) {
                this.supplierListOptions = res.data.data.list;
            }
        },
        async getProductCategory() {
            let res = await this.$request.product.getProductCategory({
                pid: 1, //酒类
            });
            if (res.data.error_code == 0) {
                this.typeList = res.data.data.list;
            }
        },
        //改变起泡状态时
        bubbleStateChange(val) {
            console.log(val);
            if (val == "静置酒") {
                this.sweetOptions = this.SweetStandOptions;
            } else {
                this.sweetOptions = this.SweetFoamingOptions;
            }
        },
        //供应商产品类别
        async productCategory() {
            let res = await this.$request.supplier.productCategory();
            if (res.data.error_code == 0) {
                this.productCategoryOptions = res.data.data;
            }
        },
        toText(val) {
            if (this.productCategoryOptions.length) {
                let obj = this.productCategoryOptions.find(
                    (item) => item.value == val
                );
                return obj ? obj.label : "";
            } else {
                return "";
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        ...this.ruleForm,
                        product_category: this.type,
                    };
                    data.product_type =
                        this.type == 1
                            ? this.ruleForm.product_type
                            : this.toText(this.type);
                    data.supplier = this.supplierListOptions.find(
                        (item) => item.id == this.ruleForm.supplier_id
                    ).name;

                    this.$request.supplier.addProduct(data).then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("添加成功");
                            this.closeDialog();
                        }
                    });
                } else {
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.f_box {
    display: flex;
    justify-content: space-between;
}
</style>

<template>
    <div>
        <el-card shadow="never">
            <div class="f_box">
                <el-select
                    v-model="query.product_category"
                    placeholder="品类"
                    size="mini"
                    class="m-r-10"
                    @change="search"
                >
                    <el-option
                        v-for="item in productCategoryOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    v-model="query.related_status"
                    placeholder="关联状态"
                    size="mini"
                    class="m-r-10"
                    clearable
                >
                    <el-option
                        v-for="item in RelatedStatusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    v-model="query.specs"
                    placeholder="规格"
                    size="mini"
                    class="w-normal m-r-10"
                ></el-input>
                <VhSelect
                    ref="vhSelec1"
                    label="商品英文名称"
                    type="product_name"
                    :productCategory="query.product_category"
                />
                <!-- :name.sync="query.product_name" -->
                <!-- <el-input
                    v-model="query.product_name"
                    placeholder="商品英文名称"
                    size="mini"
                    class="w-large m-r-10"
                ></el-input> -->
                <!-- <el-select
                    v-model="query.product_name"
                    multiple
                    filterable
                    remote
                    reserve-keyword
                    placeholder="商品英文名称"
                    :remote-method="goodsNameRemoteMethod"
                    :loading="loading"
                    size="mini"
                    class="m-r-10 w-large"
                    clearable
                >
                    <el-option
                        v-for="item in goodsOptions"
                        :key="item.id"
                        :label="item.en_product_name + '-' + item.year"
                        :value="item.en_product_name"
                    >
                    </el-option>
                </el-select> -->
                <el-input
                    v-model="query.short_code"
                    placeholder="简码"
                    size="mini"
                    class="w-normal m-r-10"
                ></el-input>
                <el-input
                    v-model="query.id"
                    placeholder="SKU_ID"
                    size="mini"
                    class="w-normal m-r-10"
                ></el-input>
                <el-input
                    v-model="query.supplier"
                    placeholder="供应商"
                    size="mini"
                    class="w-normal m-r-10"
                ></el-input>
                <VhSelect
                    ref="vhSelec2"
                    label="产区"
                    type="producing_area"
                    :productCategory="query.product_category"
                />
                <!-- <el-select
                    v-model="query.producing_area"
                    multiple
                    filterable
                    remote
                    reserve-keyword
                    placeholder="产区"
                    :remote-method="productRemoteMethod"
                    :loading="loading"
                    size="mini"
                    class="m-r-10"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in productOptions"
                        :key="index"
                        :label="item"
                        :value="item"
                    >
                    </el-option>
                </el-select> -->
                <!-- 葡萄酒、啤酒 -->
                <div v-if="[1, 2].includes(query.product_category)">
                    <el-input
                        v-model="query.stock_lt"
                        placeholder="库存最少"
                        size="mini"
                        class="w-mini"
                    ></el-input>
                    -
                    <el-input
                        v-model="query.stock_gt"
                        placeholder="库存最多"
                        size="mini"
                        class="w-mini m-r-10"
                    ></el-input>
                </div>
                <div v-if="query.product_category == 1">
                    <el-date-picker
                        v-model="query.syear"
                        type="year"
                        value-format="yyyy"
                        size="mini"
                        placeholder="葡萄采摘开始年份"
                    >
                    </el-date-picker>
                    -
                    <el-date-picker
                        v-model="query.eyear"
                        type="year"
                        value-format="yyyy"
                        class="m-r-10"
                        size="mini"
                        placeholder="葡萄采摘结束年份"
                    >
                    </el-date-picker>
                </div>

                <!-- <el-date-picker
                    v-if="query.product_category == 1"
                    v-model="time1"
                    type="datetimerange"
                    align="right"
                    size="mini"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="葡萄采摘开始年份"
                    end-placeholder="葡萄采摘结束年份"
                    :default-time="['00:00:00', '23:59:59']"
                    class="m-r-10"
                    @change="formatChange(time1, 'syear', 'eyear')"
                    clearable
                >
                </el-date-picker> -->
                <el-date-picker
                    v-model="time2"
                    type="daterange"
                    align="right"
                    size="mini"
                    value-format="yyyy-MM-dd"
                    start-placeholder="报价开始年份"
                    end-placeholder="报价结束年份"
                    class="m-r-10"
                    @change="formatChange(time2, 'squotation', 'equotation')"
                    clearable
                >
                </el-date-picker>
                <el-select
                    v-model="query.sort"
                    placeholder="排序"
                    size="mini"
                    class="m-r-10"
                    @change="search"
                    clearable
                >
                    <el-option
                        v-for="item in sortOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <!-- <el-date-picker
                    v-model="time2"
                    type="datetimerange"
                    align="right"
                    size="mini"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="报价开始年份"
                    end-placeholder="报价结束年份"
                    :default-time="['00:00:00', '23:59:59']"
                    class="m-r-10"
                    @change="formatChange(time2, 'squotation', 'equotation')"
                    clearable
                >
                </el-date-picker> -->
                <div>
                    <el-button type="warning" size="mini" @click="search"
                        >搜索</el-button
                    >
                    <el-button type="primary" size="mini" @click="add"
                        >新增</el-button
                    >
                    <el-button size="mini" @click="reset">重置</el-button>
                </div>
            </div>
        </el-card>

        <el-card shadow="never" class="m-t-20">
            <el-button
                size="mini"
                type="primary"
                @click="batchImportVisible = true"
                >批量导入</el-button
            >
            <el-button type="warning" size="mini" @click="down"
                >下载导入模版</el-button
            >

            <el-button
                type="success"
                size="mini"
                :disabled="!tableData.length"
                @click="openingForSale"
                >开售</el-button
            >

            <el-card shadow="never" class="m-t-10">
                <el-table
                    :data="tableData"
                    border
                    style="width: 100%"
                    size="small"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column width="55">
                        <div slot="header">
                            <el-checkbox
                                :disabled="
                                    !tableData.length ||
                                    tableData.every((item) => !item.product_id)
                                "
                                :value="selectionAll"
                                @change="handleSelectionAll"
                            ></el-checkbox>
                        </div>
                        <template slot-scope="{ row }">
                            <el-checkbox
                                :disabled="!row.product_id"
                                v-model="row.$selection"
                                @change="
                                    (e) => (row.$selection = e)
                                " /></template
                    ></el-table-column>
                    <el-table-column label="上次开售" width="150">
                        <template slot-scope="{ row }">
                            <span>{{
                                row.$open_time ? row.$open_time : "-"
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="id" label="SKU_ID" width="100">
                    </el-table-column>
                    <el-table-column prop="short_code" label="简码" width="150">
                        <template slot-scope="scope">
                            <el-link
                                type="primary"
                                :underline="false"
                                @click="shortCodeMatch(scope.row)"
                                >{{
                                    scope.row.short_code
                                        ? scope.row.short_code
                                        : "-"
                                }}</el-link
                            >
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="en_product_name"
                        label="英文品名"
                        width="150"
                    >
                        <template slot-scope="scope">
                            <el-link
                                type="primary"
                                :underline="false"
                                @click="goodsDetail(scope.row)"
                                >{{
                                    scope.row.en_product_name
                                        ? scope.row.en_product_name
                                        : "-"
                                }}</el-link
                            >
                        </template>
                    </el-table-column>
                    <el-table-column prop="price" label="供应商报价">
                    </el-table-column>
                    <el-table-column
                        prop="quotation_time"
                        label="报价时间"
                        width="160"
                    >
                    </el-table-column>
                    <el-table-column prop="specs" label="规格">
                    </el-table-column>
                    <el-table-column
                        prop="stock"
                        label="供应商提供库存"
                        width="130"
                        v-if="query.product_category == 1"
                    >
                        <template slot-scope="scope">
                            <span>{{
                                scope.row.stock ? scope.row.stock : "on request"
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="en_country" label="国家(英文)">
                    </el-table-column>
                    <el-table-column
                        prop="producing_area_en"
                        label="产区(英文)"
                        width="150"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="year"
                        label="葡萄采摘年份"
                        width="130"
                        v-if="[1, 6].includes(query.product_category)"
                    >
                    </el-table-column>
                    <el-table-column prop="supplier" label="供应商" width="170">
                    </el-table-column>
                    <el-table-column prop="product_category" label="品类">
                        <template slot-scope="scope">
                            <span>{{
                                toText(scope.row.product_category)
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="cn_product_name"
                        label="中文品名"
                        width="150"
                    >
                    </el-table-column>
                    <el-table-column prop="cn_country" label="国家(中文)">
                    </el-table-column>
                    <el-table-column
                        prop="producing_area_cn"
                        label="产区(中文)"
                        width="150"
                    >
                    </el-table-column>
                </el-table>
            </el-card>
        </el-card>
        <div style="text-align: center" class="m-t-10">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <!-- 商品详情 -->
        <GoodsDetail
            :visible.sync="goodsDetailVisible"
            :type="query.product_category"
            :rowData="rowData"
            @getList="supplierproductList"
        />
        <!-- 简码匹配 -->
        <ShortCodeMatch
            :visible.sync="shortCodeMatchVisible"
            :rowData="rowData"
            @getList="supplierproductList"
        />
        <!-- 新增 -->
        <Add
            :visible.sync="addVisible"
            :type="query.product_category"
            @getList="supplierproductList"
        />
        <!-- 批量导入 -->
        <BatchImport
            :visible.sync="batchImportVisible"
            @getList="supplierproductList"
        />
    </div>
</template>

<script>
import GoodsDetail from "./goodsDetail.vue";
import ShortCodeMatch from "./shortCodeMatch.vue";
import Add from "./add.vue";
import BatchImport from "./batchImport.vue";
import VhSelect from "@/components/vhSelect/index.vue";
import { RelatedStatusOptions } from "../../tools/mapper";
import toastMixin from "@/mixins/toastMixin";

export default {
    mixins: [toastMixin],
    components: {
        GoodsDetail,
        ShortCodeMatch,
        Add,
        BatchImport,
        VhSelect,
    },
    data() {
        return {
            addVisible: false,
            goodsDetailVisible: false,
            shortCodeMatchVisible: false,
            batchImportVisible: false,
            rowData: {},
            productCategoryOptions: [],
            time1: "",
            time2: "",
            query: {
                product_category: 1,
                id: "",
                related_status: "",
                specs: "",
                product_name: "",
                producing_area: "",
                supplier: "",
                stock_lt: "",
                stock_gt: "",
                syear: "",
                eyear: "",
                squotation: "",
                equotation: "",
                short_code: "",
                sort: "",
                page: 1,
                limit: 10,
            },
            // 1-葡萄年份升序 2-葡萄年份降序 3-库存升序 4-库存降序 5-报价升序 6-报价降序 7-报价时间升序 8-报价时间降序
            sortOptions: [
                {
                    value: 1,
                    label: "葡萄年份升序",
                },
                {
                    value: 2,
                    label: "葡萄年份降序",
                },
                {
                    value: 3,
                    label: "库存升序",
                },
                {
                    value: 4,
                    label: "库存降序",
                },
                {
                    value: 5,
                    label: "报价升序",
                },
                {
                    value: 6,
                    label: "报价降序",
                },
                {
                    value: 7,
                    label: "报价时间升序",
                },
                {
                    value: 8,
                    label: "报价时间降序",
                },
            ],
            total: 0,
            tableData: [],
            RelatedStatusOptions,
            loading: false,
            goodsOptions: [],
            productOptions: [],
        };
    },
    computed: {
        selectionAll({ tableData }) {
            return (
                tableData.length &&
                tableData
                    .filter((item) => item.product_id)
                    .every((item) => item.$selection)
            );
        },
    },
    mounted() {
        this.productCategory();
        this.supplierproductList();
    },
    methods: {
        //供应商商品列表
        async supplierproductList() {
            this.query.syear = this.query.syear ? this.query.syear : "";
            this.query.eyear = this.query.eyear ? this.query.eyear : "";
            let data = {
                ...this.query,
            };
            // data.product_name = data.product_name
            //     ? data.product_name.join("&*#@")
            //     : "";
            // data.producing_area = data.producing_area
            //     ? data.producing_area.join("&*#@")
            //     : "";
            let res = await this.$request.supplier.supplierproductList(data);
            if (res.data.error_code == 0) {
                const handleList = res?.data?.data?.list || [];
                handleList.forEach((item) => {
                    item.$selection = false;
                    item.$open_time = null;
                });
                this.tableData = handleList;
                this.total = res.data.data.total;
                this.batchLastSellTime();
            }
        },
        batchLastSellTime() {
            const filterList = this.tableData.filter((item) => item.short_code);
            if (filterList.length) {
                console.log("filterList", filterList);
                const short_codes = filterList
                    .map((item) => item.short_code)
                    .join(",");
                console.log(short_codes);
                this.$request.supplier
                    .batchLastSellTime({ short_codes })
                    .then((res) => {
                        if (res?.data?.error_code === 0) {
                            const openTimeInfo = res?.data?.data?.list || {};
                            const openTimeInfoKeys = Object.keys(openTimeInfo);
                            console.log(openTimeInfo);
                            this.tableData.forEach((item) => {
                                openTimeInfoKeys.forEach((item1) => {
                                    if (item.short_code === item1)
                                        item.$open_time = openTimeInfo[item1];
                                });
                            });
                            console.log(this.tableData);
                        }
                    });
            }
        },

        search() {
            console.log("===商品", this.$refs.vhSelec1);
            console.log("===产区", this.$refs.vhSelec2);
            this.query.product_name = this.$refs.vhSelec1.name;
            this.query.producing_area = this.$refs.vhSelec2.name;
            this.query.page = 1;
            this.supplierproductList();
        },
        reset() {
            this.$refs.vhSelec1.name = "";
            this.$refs.vhSelec2.name = "";
            this.query = this.$options.data().query;
            this.time1 = "";
            this.time2 = "";
        },
        down() {
            console.log(this.query.product_category);
            if (!this.query.product_category) {
                this.$message.warning("请选择品类");
                return;
            }
            switch (this.query.product_category) {
                case 1:
                    // 葡萄酒
                    window.location.href =
                        "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/wiki/supplier/%E8%91%A1%E8%90%84%E9%85%92%E5%AF%BC%E5%85%A5(1).xlsx";
                    break;
                case 6:
                    // 白兰地
                    window.location.href =
                        "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/wiki/supplier/%E7%99%BD%E5%85%B0%E5%9C%B0%E5%AF%BC%E5%85%A5.xlsx";
                    break;
                case 3:
                    // 果酒
                    window.location.href =
                        "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/wiki/supplier/%E6%9E%9C%E9%85%92%E5%AF%BC%E5%85%A5.xlsx";
                    break;
                case 4:
                    // 清酒
                    window.location.href =
                        "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/wiki/supplier/%E6%B8%85%E9%85%92%E5%AF%BC%E5%85%A5-%E6%A0%B7%E6%9C%AC%E4%BF%A1%E6%81%AF.xlsx";
                    break;
                case 2:
                    window.location.href =
                        "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/wiki/supplier/%E5%95%A4%E9%85%92%E5%AF%BC%E5%85%A5-%E6%A0%B7%E6%9C%AC%E4%BF%A1%E6%81%AF.xlsx";
                    // 啤酒
                    break;
                case 7:
                    // 威士忌
                    window.location.href =
                        "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/wiki/supplier/%E5%A8%81%E5%A3%AB%E5%BF%8C%E5%AF%BC%E5%85%A5.xlsx";
                    break;
                default:
                    window.location.href =
                        "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/wiki/supplier/%E9%BB%98%E8%AE%A4%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx";
                    break;
            }
        },
        //供应商产品类别
        async productCategory() {
            let res = await this.$request.supplier.productCategory();
            if (res.data.error_code == 0) {
                this.productCategoryOptions = res.data.data.filter(
                    (item) => item.value != 13
                );
            }
        },
        toText(val) {
            if (this.productCategoryOptions.length) {
                let obj = this.productCategoryOptions.find(
                    (item) => item.value == val
                );
                return obj ? obj.label : "";
            } else {
                return "";
            }
        },
        formatChange(time, startName, endName) {
            if (time) {
                this.query[startName] = time[0];
                this.query[endName] = time[1];
            } else {
                this.query[startName] = "";
                this.query[endName] = "";
            }
        },
        add() {
            if (!this.query.product_category) {
                this.$message.warning("请选择品类");
                return;
            }
            this.addVisible = true;
        },
        goodsDetail(row) {
            this.rowData = row;
            this.goodsDetailVisible = true;
        },
        shortCodeMatch(row) {
            this.rowData = row;
            this.shortCodeMatchVisible = true;
        },
        goodsNameRemoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.supplier
                    .searchGetId({ product_name: query })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.goodsOptions = res.data.data?.list;
                        }
                    });
            } else {
                this.goodsOptions = [];
            }
        },

        productRemoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.supplier
                    .searchGetId({ producing_area: query })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.productOptions = res.data.data?.list;
                        }
                    });
            } else {
                this.productOptions = [];
            }
        },
        handleSelectionAll() {
            const selectionAll = this.tableData
                .filter((item) => item.product_id)
                .every((item) => item.$selection);
            console.log(selectionAll);
            this.tableData
                .filter((item) => item.product_id)
                .forEach((item) => (item.$selection = !selectionAll));
        },
        openingForSale() {
            const multipleSelection = this.tableData.filter(
                (item) => item.$selection
            );
            if (!multipleSelection.length)
                return this.toast("error", "请先勾开售项！");
            console.log(multipleSelection);
            const products = multipleSelection.map(
                ({ id: sku_id, supplier, supplier_id, product_id }) => ({
                    sku_id,
                    supplier,
                    supplier_id,
                    product_id,
                })
            );
            this.$request.supplier.onSale({ products }).then((res) => {
                if (res?.data?.error_code === 0) {
                    this.toast("success", "开售成功");
                    this.supplierproductList();
                }
            });
            console.log(products);
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.supplierproductList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.query.page = val;
            this.supplierproductList();
        },
    },
};
</script>

<style lang="scss" scoped>
.f_box {
    display: flex;
    justify-content: left;
    flex-wrap: wrap;
}
</style>

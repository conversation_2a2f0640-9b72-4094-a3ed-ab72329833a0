<template>
    <div>
        <el-dialog
            title="简码匹配"
            :visible="visible"
            width="60%"
            :before-close="closeDialog"
            :close-on-click-modal="false"
        >
            <el-card shadow="never">
                <el-input
                    v-model="ruleForm.short_code"
                    placeholder="请输入简码"
                    size="mini"
                    class="w-large m-r-10"
                ></el-input>
                <el-input
                    v-model="ruleForm.en_product_name"
                    placeholder="请输入英文品名"
                    size="mini"
                    class="w-large m-r-10"
                ></el-input>
                <el-button type="primary" size="mini" @click="search"
                    >搜索</el-button
                >
            </el-card>
            <el-card shadow="never" class="m-t-20">
                <el-table :data="tableData" style="width: 100%">
                    <el-table-column type="expand">
                        <template slot-scope="props">
                            <div class="f_box">
                                <div>
                                    商品类型：{{ props.row.product_type_name }}
                                </div>
                                <div>
                                    采摘年份：{{
                                        props.row.grape_picking_years
                                    }}
                                </div>
                                <div>残糖：{{ props.row.residual_sugar }}</div>
                                <div>国家：{{ props.row.country_name_cn }}</div>
                                <div>产区：{{ props.row.regions_name_cn }}</div>
                                <div>酒精度：{{ props.row.alcohol }}</div>
                                <div>酒庄：{{ props.row.chateau_name }}</div>
                                <div>规格：{{ props.row.capacity }}</div>
                                <div>
                                    关键词：{{ props.row.product_keywords }}
                                </div>
                                <div>
                                    商品单位：{{ props.row.product_unit_name }}
                                </div>
                                <div>保质期：{{ props.row.shelf_life }}</div>
                                <div>酿造工艺：{{ props.row.brewing }}</div>
                                <div>
                                    罐装日期：{{ props.row.canning_years }}
                                </div>
                                <div>
                                    箱规：{{ props.row.carton_dimension }}
                                </div>
                                <div>
                                    Tasting Notes：{{ props.row.tasting_notes }}
                                </div>
                                <div>
                                    商品形态：{{ props.row.product_form }}
                                </div>
                                <div>成本：{{ props.row.costprice }}</div>
                                <div>
                                    整箱成本：{{ props.row.cost_container }}
                                </div>
                                <div>税率：{{ props.row.tax_rate }}</div>
                                <div>评分：{{ props.row.score }}</div>
                                <div>
                                    指令商品：{{
                                        props.row.is_gift ? "是" : "否"
                                    }}
                                </div>
                                <div>获奖：{{ props.row.prize }}</div>
                                <div>
                                    创建时间：{{ props.row.created_time }}
                                </div>
                                <div>创建人：{{ props.row.adname }}</div>
                                <div>
                                    最近修改时间：{{ props.row.update_time }}
                                </div>
                                <div>
                                    最近修改人：{{ props.row.reviser_name }}
                                </div>
                                <div>重量：{{ props.row.weight }}</div>
                                <div>
                                    饮用建议：{{
                                        props.row.drinking_suggestion
                                    }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="简码"
                        prop="short_code"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="中文品名"
                        prop="cn_product_name"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="英文品名"
                        prop="en_product_name"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        fixed="right"
                        label="操作"
                        width="120"
                    >
                        <template slot-scope="scope">
                            <el-button
                                @click="shortCodeMatch(scope.row)"
                                type="text"
                            >
                                匹配关联
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
            <div style="text-align: center" class="m-t-10">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="ruleForm.page"
                    :page-size="ruleForm.limit"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import dialogmixin from "../../mixins/dialogmixin";
export default {
    props: ["rowData"],
    mixins: [dialogmixin],
    data() {
        return {
            ruleForm: {
                short_code: "",
                en_product_name: "",
                page: 1,
                limit: 10,
            },
            total: 0,
            tableData: [],
        };
    },
    watch: {
        visible(val) {
            console.log(val, this.rowData.id);
            if (val) {
                this.total = 0;
                this.ruleForm = this.$options.data().ruleForm;
                this.tableData = [];
            }
        },
    },
    mounted() {
        // this.getProducts();
    },
    methods: {
        async getProducts() {
            if (!this.ruleForm.short_code && !this.ruleForm.en_product_name) {
                this.$message.warning("请填写简码或名称");
                return;
            }
            let res = await this.$request.supplier.getProducts(this.ruleForm);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        search() {
            this.ruleForm.page = 1;
            this.getProducts();
        },
        async shortCodeMatch(row) {
            this.$confirm("确定要关联该产品所对应的简码?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    console.log(row);
                    let data = {
                        id: this.rowData.id,
                        product_id: row.id,
                        short_code: row.short_code,
                        country_id: row.country_id,
                        producing_area_id: row.producing_area_id,
                    };
                    console.log(data);
                    this.$request.supplier
                        .relatedShortCode(data)
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("匹配成功");
                                // this.getProducts();
                                this.closeDialog();
                            }
                        });
                })
                .catch(() => {
                    console.log("error");
                });
        },
        handleSizeChange(val) {
            this.ruleForm.page = 1;
            this.ruleForm.limit = val;
            //   this.supplierproductList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.ruleForm.page = val;
            //   this.supplierproductList();
        },
    },
};
</script>

<style lang="scss" scoped>
.demo-table-expand {
    font-size: 0;
}
.demo-table-expand label {
    width: 90px;
    color: #99a9bf;
}
.demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
}
.f_box {
    padding-left: 20px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    div {
        width: 300px;
        color: #919eae;
        margin: 1px 0;
    }
}
</style>

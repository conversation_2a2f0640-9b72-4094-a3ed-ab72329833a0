<template>
    <div>
        <el-dialog
            title="商品详情"
            :visible="visible"
            width="70%"
            :before-close="closeDialog"
            :close-on-click-modal="false"
        >
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                label-width="130px"
                class="demo-ruleForm"
                size="mini"
            >
                <el-divider content-position="left">基本信息</el-divider>
                <div class="f_box">
                    <div>
                        <el-form-item label="中文品名" prop="cn_product_name">
                            <el-input
                                v-model="ruleForm.cn_product_name"
                                placeholder="请输入中文品名"
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            :label="`供应商报价 (${ruleForm.currency})`"
                            prop="price"
                        >
                            <el-input
                                v-model="ruleForm.price"
                                placeholder="请输入供应商报价"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="规格" prop="specs">
                            <el-input
                                v-model="ruleForm.specs"
                                placeholder="请输入规格"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="单位" prop="pack">
                            <el-select
                                v-model="ruleForm.pack"
                                filterable
                                placeholder="请选择单位"
                            >
                                <el-option
                                    v-for="item in PackWineOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <!-- 葡萄酒 -->
                        <div v-if="type == 1">
                            <el-form-item label="颜色" prop="color">
                                <el-select
                                    v-model="ruleForm.color"
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="item in ColorOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="起泡状态" prop="bubble_state">
                                <el-select
                                    v-model="ruleForm.bubble_state"
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="item in FoamingOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item
                                label="加强类型"
                                prop="intensify_type"
                            >
                                <el-select
                                    v-model="ruleForm.intensify_type"
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="item in IntensifyTypeOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item
                                label="箱规包装"
                                prop="box_gauge_package"
                            >
                                <el-select
                                    v-model="ruleForm.box_gauge_package"
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="item in BoxGaugePackageOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <!-- 啤酒 -->
                        <div v-if="type == 2">
                            <el-form-item label="酒款风格" prop="wine_style">
                                <el-input
                                    v-model="ruleForm.wine_style"
                                    placeholder="请输入酒款风格"
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="苦度" prop="bitter_extent">
                                <el-input
                                    v-model="ruleForm.bitter_extent"
                                    placeholder="请输入苦度"
                                ></el-input>
                            </el-form-item>
                        </div>
                        <!-- 清酒 -->
                        <div v-if="type == 4">
                            <el-form-item
                                label="精米步和"
                                prop="jingmi_stepping"
                            >
                                <el-input
                                    v-model="ruleForm.jingmi_stepping"
                                    placeholder="请输入精米步和"
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="米种" prop="rice_seed">
                                <el-input
                                    v-model="ruleForm.rice_seed"
                                    placeholder="请输入米种"
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="酸度" prop="acidity">
                                <el-input
                                    v-model="ruleForm.acidity"
                                    placeholder="请输入酸度"
                                ></el-input>
                            </el-form-item>
                        </div>
                        <!-- 威士忌 -->
                        <div v-if="type == 7">
                            <el-form-item label="装瓶年份" prop="bottled_year">
                                <el-date-picker
                                    v-model="ruleForm.bottled_year"
                                    type="year"
                                    placeholder="选择年"
                                    value-format="yyyy"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </div>
                    </div>
                    <div style="margin-left: 8%">
                        <el-form-item label="英文品名" prop="en_product_name">
                            <el-input
                                v-model="ruleForm.en_product_name"
                                class="w-large"
                                placeholder="请输入英文品名"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="报价时间" prop="quotation_time">
                            <el-date-picker
                                v-model="ruleForm.quotation_time"
                                type="date"
                                align="right"
                                size="mini"
                                value-format="yyyy-MM-dd"
                                placeholder="选择报价时间"
                                class="m-r-10"
                                style="width: 100%"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="进口类型" prop="import_type">
                            <el-select
                                v-model="ruleForm.import_type"
                                filterable
                                placeholder="请选择进口类型"
                            >
                                <el-option
                                    v-for="item in ImportTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <!-- 威士忌 -->
                        <div v-if="type == 7">
                            <el-form-item label="酒龄" prop="alcoholic_age">
                                <el-input
                                    v-model="ruleForm.alcoholic_age"
                                    class="w-large"
                                    placeholder="请输入酒龄"
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="桶型" prop="barrel_shape">
                                <el-select
                                    v-model="ruleForm.barrel_shape"
                                    filterable
                                    placeholder="请选择桶型"
                                >
                                    <el-option
                                        v-for="item in BarrelShapeOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <!-- 葡萄酒、白兰地 -->
                        <div v-if="[1, 6].includes(type)">
                            <el-form-item label="葡萄采摘年份" prop="year">
                                <el-date-picker
                                    v-model="ruleForm.year"
                                    type="year"
                                    value-format="yyyy"
                                    class="m-r-10"
                                    style="width: 100%"
                                    placeholder="请选择葡萄采摘年份"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <!-- 啤酒、清酒 -->
                        <div v-if="[2, 4].includes(type)">
                            <el-form-item label="罐装日期" prop="bottled_year">
                                <el-date-picker
                                    v-model="ruleForm.bottled_year"
                                    size="mini"
                                    value-format="yyyy-MM-dd"
                                    type="date"
                                    class="m-r-10"
                                    style="width: 100%"
                                    placeholder="选择罐装日期"
                                    :picker-options="pickerOptions"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <!-- 葡萄酒 -->
                        <div v-if="type == 1">
                            <el-form-item label="甜度" prop="sweetness">
                                <!-- <el-input
                                    v-model="ruleForm.sweetness"
                                    class="w-large"
                                    placeholder="请输入甜度"
                                ></el-input> -->

                                <el-select
                                    v-model="ruleForm.sweetness"
                                    filterable
                                    placeholder="请选择甜度"
                                >
                                    <el-option
                                        v-for="item in ruleForm.bubble_state ==
                                        '静置酒'
                                            ? SweetStandOptions
                                            : SweetFoamingOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="供应商提供库存" prop="stock">
                                <!-- <el-input
                                    v-model="ruleForm.stock"
                                    class="w-large"
                                    placeholder="请输入供应商提供库存"
                                ></el-input> -->
                                <el-input-number
                                    v-model="ruleForm.stock"
                                    :controls="false"
                                    :precision="0"
                                    class="w-normal"
                                    placeholder="请输入供应商库存"
                                ></el-input-number>
                            </el-form-item>
                            <el-form-item label="品类" prop="product_category">
                                <el-select
                                    v-model="ruleForm.product_category"
                                    placeholder="品类"
                                    size="mini"
                                    class="m-r-10"
                                    disabled
                                >
                                    <el-option
                                        v-for="item in productCategoryOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                                <!-- <el-input
                                    v-model="ruleForm.product_category"
                                    class="w-large"
                                    placeholder="请输入品类"
                                    disabled
                                ></el-input> -->
                            </el-form-item>
                        </div>
                        <!-- 啤酒 -->
                        <div v-if="type == 2">
                            <el-form-item
                                label="原麦汁浓度"
                                prop="original_gravity"
                            >
                                <el-input
                                    v-model="ruleForm.original_gravity"
                                    class="w-large"
                                    placeholder="请输入原麦汁浓度"
                                ></el-input>
                            </el-form-item>
                        </div>
                        <!-- 果酒 -->
                        <div v-if="type == 3">
                            <el-form-item label="水果" prop="fruit">
                                <el-input
                                    v-model="ruleForm.fruit"
                                    class="w-large"
                                    placeholder="请输入水果"
                                ></el-input>
                            </el-form-item>
                        </div>
                        <!-- 清酒 -->
                        <div v-if="type == 4">
                            <el-form-item label="生产日期" prop="produce_time">
                                <el-date-picker
                                    v-model="ruleForm.produce_time"
                                    type="date"
                                    align="right"
                                    size="mini"
                                    value-format="yyyy-MM-dd"
                                    placeholder="请选择生产日期"
                                    class="m-r-10"
                                    style="width: 100%"
                                    :picker-options="pickerOptions"
                                >
                                </el-date-picker>
                                <!-- <el-date-picker
                                    v-model="ruleForm.produce_time"
                                    type="datetime"
                                    align="right"
                                    size="mini"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    placeholder="请选择生产日期"
                                    class="m-r-10"
                                    style="width: 100%"
                                >
                                </el-date-picker> -->
                            </el-form-item>
                            <el-form-item
                                label="日本酒度"
                                prop="japanese_alcohol_content"
                            >
                                <el-input
                                    v-model="ruleForm.japanese_alcohol_content"
                                    placeholder="请输入日本酒度"
                                ></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="m-l-10">
                        <el-link
                            type="primary"
                            :underline="false"
                            @click="standardGoodsInfoVisible = true"
                            v-if="rowData.short_code"
                            >绑定简码：{{ rowData.short_code }}</el-link
                        >
                    </div>
                </div>

                <el-divider content-position="left">拓展信息</el-divider>

                <div class="f_box">
                    <div style="width: 40%">
                        <el-form-item label="供应商" prop="supplier_id">
                            <!-- <el-select
                                v-model="ruleForm.supplier_id"
                                filterable
                                placeholder="请选择供应商"
                            >
                                <el-option
                                    v-for="item in supplierListOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select> -->
                            <el-select
                                v-model="ruleForm.supplier_id"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入供应商"
                                :remote-method="remoteMethod"
                                :loading="loading"
                                size="mini"
                                class="m-r-10"
                                clearable
                            >
                                <el-option
                                    v-for="item in supplierListOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="国家（中文）" prop="cn_country">
                            <el-input
                                v-model="ruleForm.cn_country"
                                placeholder="请输入国家（中文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            label="产区（中文）"
                            prop="producing_area_cn"
                        >
                            <el-input
                                v-model="ruleForm.producing_area_cn"
                                placeholder="请输入产区（中文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="正标" prop="frontal_label_img">
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :limit="1"
                                :dir="dir"
                                :file-list="frontal_label_img"
                                :disabled="true"
                                ref="vos1"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                        </el-form-item>
                        <el-form-item label="英文背标" prop="en_back_label_img">
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :limit="1"
                                :dir="dir"
                                :file-list="en_back_label_img"
                                :disabled="true"
                                ref="vos2"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                        </el-form-item>
                        <el-form-item label="产品图" prop="product_image">
                            <vos-oss
                                list-type="text"
                                :showFileList="true"
                                :limit="9"
                                :dir="dir"
                                :file-list="product_image"
                                ref="vos3"
                            >
                                <el-button size="mini" type="success"
                                    >上传附件</el-button
                                >
                            </vos-oss>
                        </el-form-item>
                    </div>
                    <div style="width: 50%; margin-left: 8%">
                        <el-form-item label="国家（英文）" prop="en_country">
                            <el-input
                                v-model="ruleForm.en_country"
                                placeholder="请输入国家（英文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            label="产区（英文）"
                            prop="producing_area_en"
                        >
                            <el-input
                                v-model="ruleForm.producing_area_en"
                                placeholder="请输入产区（英文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="中文背标" prop="cn_back_label_img">
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :limit="1"
                                :dir="dir"
                                :file-list="cn_back_label_img"
                                :disabled="true"
                                ref="vos4"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                        </el-form-item>
                        <el-form-item label="附件" prop="product_attachment">
                            <div class="w-max-large">
                                <el-link
                                    type="primary"
                                    :underline="false"
                                    v-for="(
                                        item, index
                                    ) in ruleForm.product_attachment"
                                    :key="index"
                                    @click="open(item)"
                                    >{{ item }}</el-link
                                >
                            </div>
                        </el-form-item>
                    </div>
                </div>

                <div style="text-align: center; margin-left: -130px">
                    <el-form-item>
                        <el-button @click="closeDialog">取消</el-button>
                        <el-button
                            type="primary"
                            @click="submitForm('ruleForm')"
                            >确定</el-button
                        >
                    </el-form-item>
                </div>
            </el-form>
        </el-dialog>
        <StandardGoodsInfo
            :visible.sync="standardGoodsInfoVisible"
            :rowData="ruleForm"
            :type="type"
        />
    </div>
</template>

<script>
import dialogmixin from "../../mixins/dialogmixin";
import vosOss from "vos-oss";
import StandardGoodsInfo from "./standardGoodsInfo.vue";
import {
    ColorOptions,
    FoamingOptions,
    IntensifyTypeOptions,
    BoxGaugePackageOptions,
    PackWineOptions,
    ImportTypeOptions,
    BarrelShapeOptions,
    SweetStandOptions,
    SweetFoamingOptions,
} from "../../tools/mapper";
export default {
    components: {
        vosOss,
        StandardGoodsInfo,
    },
    mixins: [dialogmixin],
    props: ["type", "rowData"],
    data() {
        const checkProductImage = (rule, value, callback) => {
            if (!this.product_image.length) {
                callback(new Error("请上传产品图"));
            } else {
                callback();
            }
        };
        return {
            pickerOptions: {
                //返回true不可选，返回false可选
                disabledDate: (time) => {
                    return time.getTime() > Date.now();
                },
            },
            dir: "vinehoo/wiki/supplier/",
            standardGoodsInfoVisible: false,
            supplierListOptions: [],
            productCategoryOptions: [],
            ColorOptions, //颜色
            FoamingOptions, //起泡状态
            IntensifyTypeOptions, //加强状态
            BoxGaugePackageOptions, //箱规包装
            PackWineOptions,
            ImportTypeOptions,
            BarrelShapeOptions,
            SweetStandOptions,
            SweetFoamingOptions,
            frontal_label_img: [],
            en_back_label_img: [],
            product_image: [],
            cn_back_label_img: [],
            loading: false,
            ruleForm: {
                cn_product_name: "",
                price: "",
                specs: "",
                color: "",
                intensify_type: "",
                box_gauge_package: "",
                wine_style: "",
                bitter_extent: "",
                jingmi_stepping: "",
                en_product_name: "",
                quotation_time: "",
                year: "",
                sweetness: "",
                stock: "",
                product_category: "",
                original_gravity: "",
                fruit: "",
                produce_time: "",
                supplier_id: "",
                cn_country: "",
                producing_area_cn: "",
                en_country: "",
                producing_area_en: "",
                product_attachment: "",
                bubble_state: "",
                rice_seed: "",
                bottled_year: "",
                pack: "",
                import_type: "",
                acidity: "",
                japanese_alcohol_content: "",
                alcoholic_age: "",
                barrel_shape: "",
            },
            rules: {
                product_image: [
                    {
                        required: true,
                        validator: checkProductImage,
                        trigger: "blur",
                    },
                ],
                cn_product_name: [
                    {
                        required: true,
                        message: "请输入中文品名",
                        trigger: "blur",
                    },
                ],
                price: [
                    {
                        required: true,
                        message: "请输入供应商报价",
                        trigger: "blur",
                    },
                ],
                specs: [
                    {
                        required: true,
                        message: "请输入规格",
                        trigger: "blur",
                    },
                ],
                color: [
                    {
                        required: true,
                        message: "请选择颜色",
                        trigger: "change",
                    },
                ],
                intensify_type: [
                    {
                        required: true,
                        message: "请选择加强类型",
                        trigger: "change",
                    },
                ],
                box_gauge_package: [
                    {
                        required: true,
                        message: "请选择箱规包装",
                        trigger: "change",
                    },
                ],
                wine_style: [
                    {
                        required: true,
                        message: "请输入酒款风格",
                        trigger: "blur",
                    },
                ],
                bitter_extent: [
                    {
                        required: true,
                        message: "请输入苦度",
                        trigger: "blur",
                    },
                ],
                jingmi_stepping: [
                    {
                        required: true,
                        message: "请选择精米步合",
                        trigger: "blur",
                    },
                ],
                en_product_name: [
                    {
                        required: true,
                        message: "请输入英文品名",
                        trigger: "blur",
                    },
                ],
                quotation_time: [
                    {
                        required: true,
                        message: "请选择报价时间",
                        trigger: "blur",
                    },
                ],
                year: [
                    {
                        required: true,
                        message: "请选择葡萄采摘年份",
                        trigger: "blur",
                    },
                ],
                sweetness: [
                    {
                        required: true,
                        message: "请输入甜度",
                        trigger: "blur",
                    },
                ],
                stock: [
                    {
                        required: true,
                        message: "请输入库存",
                        trigger: "blur",
                    },
                ],
                product_category: [
                    {
                        required: true,
                        message: "请选择品类",
                        trigger: "change",
                    },
                ],
                original_gravity: [
                    {
                        required: true,
                        message: "请选择原麦汁浓度",
                        trigger: "blur",
                    },
                ],
                fruit: [
                    {
                        required: true,
                        message: "请选择水果",
                        trigger: "blur",
                    },
                ],
                produce_time: [
                    {
                        required: true,
                        message: "请选择生产日期",
                        trigger: "blur",
                    },
                ],
                supplier_id: [
                    {
                        required: true,
                        message: "请选择供应商",
                        trigger: "change",
                    },
                ],
                cn_country: [
                    {
                        required: true,
                        message: "请输入国家（中文）",
                        trigger: "blur",
                    },
                ],
                producing_area_cn: [
                    {
                        required: true,
                        message: "请输入产区（中文）",
                        trigger: "blur",
                    },
                ],
                en_country: [
                    {
                        required: true,
                        message: "请输入国家（英文）",
                        trigger: "blur",
                    },
                ],
                producing_area_en: [
                    {
                        required: true,
                        message: "请输入产区（英文）",
                        trigger: "blur",
                    },
                ],
                bubble_state: [
                    {
                        required: true,
                        message: "请选择起泡状态",
                        trigger: "change",
                    },
                ],
                rice_seed: [
                    {
                        required: true,
                        message: "请输入米种",
                        trigger: "blur",
                    },
                ],
                bottled_year: [
                    {
                        required: true,
                        message: "请选择罐装日期",
                        trigger: "blur",
                    },
                ],
                pack: [
                    {
                        required: true,
                        message: "请选择单位",
                        trigger: "change",
                    },
                ],
                import_type: [
                    {
                        required: true,
                        message: "请选择进口类型",
                        trigger: "change",
                    },
                ],
                acidity: [
                    {
                        required: true,
                        message: "请输入酸度",
                        trigger: "blur",
                    },
                ],
                japanese_alcohol_content: [
                    {
                        required: true,
                        message: "请输入日本酒度",
                        trigger: "blur",
                    },
                ],
                alcoholic_age: [
                    {
                        required: true,
                        message: "请输入酒龄",
                        trigger: "blur",
                    },
                ],
                barrel_shape: [
                    {
                        required: true,
                        message: "请选择桶型",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    watch: {
        visible(val) {
            console.log(val, this.type);
            if (val) {
                this.ruleForm = this.$options.data().ruleForm;
                this.$nextTick(() => {
                    this.$refs.vos1.handleviewFileList([]);
                    this.$refs.vos2.handleviewFileList([]);
                    this.$refs.vos3.handleviewFileList([]);
                    this.$refs.vos4.handleviewFileList([]);
                    this.frontal_label_img = [];
                    this.en_back_label_img = [];
                    this.product_image = [];
                    this.cn_back_label_img = [];
                    this.$refs.ruleForm.resetFields();
                });
                // this.supplierList();
                this.productCategory();
                this.goodsDetail();
            }
        },
    },
    methods: {
        open(url) {
            window.open(url, "_blank");
        },
        async goodsDetail() {
            console.log(this.rowData);
            let res = await this.$request.supplier.goodsDetail({
                id: this.rowData.id,
            });
            if (res.data.error_code == 0) {
                this.ruleForm = res.data.data;
                this.frontal_label_img = res.data.data.frontal_label_img
                    ? res.data.data.frontal_label_img.split(",")
                    : [];
                this.en_back_label_img = res.data.data.en_back_label_img
                    ? res.data.data.en_back_label_img.split(",")
                    : [];
                this.product_image = res.data.data.product_image
                    ? res.data.data.product_image.split(",")
                    : [];
                this.cn_back_label_img = res.data.data.cn_back_label_img
                    ? res.data.data.cn_back_label_img.split(",")
                    : [];
                this.ruleForm.product_attachment = res.data.data
                    .product_attachment
                    ? res.data.data.product_attachment.split(",")
                    : [];
                this.remoteMethod(this.ruleForm.supplier);
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let {
                        cn_product_name,
                        price,
                        specs,
                        color,
                        intensify_type,
                        box_gauge_package,
                        wine_style,
                        bitter_extent,
                        jingmi_stepping,
                        en_product_name,
                        quotation_time,
                        year,
                        sweetness,
                        stock,
                        product_category,
                        original_gravity,
                        fruit,
                        produce_time,
                        supplier_id,
                        cn_country,
                        producing_area_cn,
                        en_country,
                        producing_area_en,
                        product_attachment,
                        bubble_state,
                        rice_seed,
                        bottled_year,
                        pack,
                        import_type,
                        acidity,
                        japanese_alcohol_content,
                        alcoholic_age,
                        barrel_shape,
                    } = this.ruleForm;
                    let data = {
                        id: this.rowData.id,
                        product_image: this.product_image.join(","),
                        cn_product_name,
                        price,
                        specs,
                        color,
                        intensify_type,
                        box_gauge_package,
                        wine_style,
                        bitter_extent,
                        jingmi_stepping,
                        en_product_name,
                        quotation_time,
                        year,
                        sweetness,
                        stock,
                        product_category,
                        original_gravity,
                        fruit,
                        produce_time,
                        supplier_id,
                        cn_country,
                        producing_area_cn,
                        en_country,
                        producing_area_en,
                        product_attachment,
                        bubble_state,
                        rice_seed,
                        bottled_year,
                        pack,
                        import_type,
                        acidity,
                        japanese_alcohol_content,
                        alcoholic_age,
                        barrel_shape,
                    };
                    data.supplier = this.supplierListOptions.find(
                        (item) => item.id == this.ruleForm.supplier_id
                    ).name;
                    this.$request.supplier
                        .goodsDetailUpdate(data)
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("修改成功");
                                this.closeDialog();
                            }
                        });
                } else {
                    return false;
                }
            });
        },
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                //  supplierList
                this.$request.supplier
                    .allListSupplier({ page: 1, limit: 50, name: query })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.supplierListOptions = res.data.data.list;
                        }
                        this.loading = false;
                    });
            } else {
                this.supplierListOptions = [];
            }
        },
        //供应商列表
        async supplierList() {
            //  supplierList
            let res = await this.$request.supplier.allListSupplier();
            if (res.data.error_code == 0) {
                this.supplierListOptions = res.data.data.list;
            }
        },
        //供应商产品类别
        async productCategory() {
            let res = await this.$request.supplier.productCategory();
            if (res.data.error_code == 0) {
                this.productCategoryOptions = res.data.data;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.f_box {
    display: flex;
    justify-content: left;
    flex-wrap: wrap;
}
.w-max-large {
    width: 100%;
}
</style>

<template>
    <div>
        <el-card shadow="never">
            <div class="f_box">
                <el-select
                    v-model="query.related_status"
                    placeholder="关联状态"
                    size="mini"
                    class="m-r-10"
                    clearable
                >
                    <el-option
                        v-for="item in RelatedStatusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <VhSelect
                    ref="vhSelec1"
                    label="商品英文名称"
                    type="product_name"
                    :productCategory="query.product_category"
                />
                <!-- <el-select
                    v-model="query.product_name"
                    multiple
                    filterable
                    remote
                    reserve-keyword
                    placeholder="商品英文名称"
                    :remote-method="goodsNameRemoteMethod"
                    :loading="loading"
                    size="mini"
                    class="m-r-10 w-large"
                    clearable
                >
                    <el-option
                        v-for="item in goodsOptions"
                        :key="item.id"
                        :label="item.en_product_name + '-' + item.year"
                        :value="item.id"
                    >
                    </el-option>
                </el-select> -->
                <el-select
                    v-model="query.product_type"
                    placeholder="产品类型"
                    size="mini"
                    class="m-r-10"
                    clearable
                >
                    <el-option
                        v-for="item in ProductTypeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    v-model="query.product_form"
                    placeholder="产品形态"
                    size="mini"
                    class="m-r-10"
                    clearable
                >
                    <el-option
                        v-for="item in ProductFormOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    v-model="query.pack"
                    placeholder="产品单位"
                    size="mini"
                    class="m-r-10"
                    clearable
                >
                    <el-option
                        v-for="item in PackFoodOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    v-model="query.storage_methods"
                    placeholder="存储方式"
                    size="mini"
                    class="m-r-10"
                    clearable
                >
                    <el-option
                        v-for="item in SaveModeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    v-model="query.id"
                    placeholder="SKU_ID"
                    size="mini"
                    class="w-mini m-r-10"
                ></el-input>
                <el-input
                    v-model="query.short_code"
                    placeholder="简码"
                    size="mini"
                    class="w-normal m-r-10"
                ></el-input>
                <el-input
                    v-model="query.shelf_life_gt"
                    placeholder="保质期最少"
                    size="mini"
                    class="w-mini m-r-5"
                ></el-input>
                -
                <el-input
                    v-model="query.shelf_life_lt"
                    placeholder="保质期最多"
                    size="mini"
                    class="w-mini m-l-5 m-r-10"
                ></el-input>
                <el-input
                    v-model="query.supplier"
                    placeholder="供应商"
                    size="mini"
                    class="w-normal m-r-10"
                ></el-input>
                <el-input
                    v-model="query.producer"
                    placeholder="生产商"
                    size="mini"
                    class="w-normal m-r-10"
                ></el-input>

                <el-input
                    v-model="query.producing_area_cn"
                    placeholder="生产地址"
                    size="mini"
                    class="w-large m-r-10"
                ></el-input>
                <el-select
                    v-model="query.sort"
                    placeholder="排序"
                    size="mini"
                    class="m-r-10"
                    @change="search"
                    clearable
                >
                    <el-option
                        v-for="item in sortOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <!-- <el-date-picker
                    v-model="time2"
                    type="datetimerange"
                    align="right"
                    size="mini"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="报价开始年份"
                    end-placeholder="报价结束年份"
                    :default-time="['00:00:00', '23:59:59']"
                    class="m-r-10"
                    @change="formatChange(time2, 'squotation', 'equotation')"
                >
                </el-date-picker> -->

                <div>
                    <el-button type="warning" size="mini" @click="search"
                        >搜索</el-button
                    >
                    <el-button
                        type="primary"
                        size="mini"
                        @click="addVisible = true"
                        >新增</el-button
                    >
                    <el-button size="mini" @click="reset">重置</el-button>
                </div>
            </div>
        </el-card>

        <el-card shadow="never" class="m-t-20">
            <div class="f_box">
                <!-- <el-button
                size="mini"
                type="primary"
                @click="batchImportVisible = true"
                >批量导入</el-button
            > -->
                <vos-oss
                    @on-success="handleSuccess"
                    ref="vos"
                    filesType="/"
                    listType="text"
                    :showFileList="false"
                    :dir="dir"
                    :file-list="file_list"
                    :limit="1"
                >
                    <el-button size="mini" type="success">批量导入</el-button>
                </vos-oss>
                <el-button
                    type="warning"
                    size="mini"
                    class="m-l-10"
                    @click="down"
                    >下载导入模版</el-button
                >
                <el-button
                    type="success"
                    size="mini"
                    :disabled="!tableData.length"
                    @click="openingForSale"
                    >开售</el-button
                >
            </div>

            <el-card shadow="never" class="m-t-10">
                <el-table
                    :data="tableData"
                    border
                    style="width: 100%"
                    size="small"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column width="55">
                        <div slot="header">
                            <el-checkbox
                                :disabled="
                                    !tableData.length ||
                                    tableData.every((item) => !item.product_id)
                                "
                                :value="selectionAll"
                                @change="handleSelectionAll"
                            ></el-checkbox>
                        </div>
                        <template slot-scope="{ row }">
                            <el-checkbox
                                :disabled="!row.product_id"
                                v-model="row.$selection"
                                @change="
                                    (e) => (row.$selection = e)
                                " /></template
                    ></el-table-column>
                    <el-table-column label="上次开售" width="150">
                        <template slot-scope="{ row }">
                            <span>{{
                                row.$open_time ? row.$open_time : "-"
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="id" label="SKU_ID" width="100">
                    </el-table-column>
                    <el-table-column prop="short_code" label="简码" width="150">
                        <template slot-scope="scope">
                            <el-link
                                type="primary"
                                :underline="false"
                                @click="shortCodeMatch(scope.row)"
                                >{{
                                    scope.row.short_code
                                        ? scope.row.short_code
                                        : "-"
                                }}</el-link
                            >
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="cn_product_name"
                        label="商品名称(中文)"
                        width="150"
                    >
                        <template slot-scope="scope">
                            <el-link
                                type="primary"
                                :underline="false"
                                @click="goodsDetail(scope.row)"
                                >{{
                                    scope.row.cn_product_name
                                        ? scope.row.cn_product_name
                                        : "-"
                                }}</el-link
                            >
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="en_product_name"
                        label="商品名称(英文)"
                        width="150"
                    >
                    </el-table-column>
                    <el-table-column prop="product_type" label="产品类型">
                    </el-table-column>
                    <el-table-column prop="product_form" label="产品形态">
                    </el-table-column>
                    <el-table-column prop="specs" label="规格">
                    </el-table-column>
                    <el-table-column prop="pack" label="产品单位">
                    </el-table-column>
                    <el-table-column
                        prop="price"
                        label="供应商报价"
                        width="130"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="stock"
                        label="供应商提供库存"
                        width="130"
                    >
                        <template slot-scope="scope">
                            <span>{{
                                scope.row.stock ? scope.row.stock : "on request"
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="en_country"
                        label="国家(英文)"
                        width="150"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="cn_country"
                        label="国家(中文)"
                        width="150"
                    >
                    </el-table-column>
                    <el-table-column prop="producer" label="生产商">
                    </el-table-column>
                    <el-table-column
                        prop="producing_area_cn"
                        label="生产地址"
                        width="170"
                    >
                    </el-table-column>
                    <el-table-column prop="supplier" label="供应商" width="170">
                    </el-table-column>
                    <el-table-column
                        prop="storage_methods"
                        label="存储方式"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="shelf_life"
                        label="保质期"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="produce_time"
                        label="生产日期"
                        width="150"
                    >
                    </el-table-column>
                </el-table>
            </el-card>
        </el-card>
        <div style="text-align: center" class="m-t-10">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <!-- 商品详情 -->
        <GoodsDetail
            :visible.sync="goodsDetailVisible"
            :type="query.product_category"
            :rowData="rowData"
            @getList="supplierproductList"
        />
        <!-- 简码匹配 -->
        <ShortCodeMatch
            :visible.sync="shortCodeMatchVisible"
            :rowData="rowData"
            @getList="supplierproductList"
        />
        <!-- 新增 -->
        <Add
            :visible.sync="addVisible"
            :type="query.product_category"
            @getList="supplierproductList"
        />
    </div>
</template>

<script>
import vosOss from "vos-oss";
import GoodsDetail from "./goodsDetail.vue";
import ShortCodeMatch from "./shortCodeMatch.vue";
import Add from "./add.vue";
import VhSelect from "@/components/vhSelect/index.vue";
import toastMixin from "@/mixins/toastMixin";

import {
    RelatedStatusOptions,
    PackFoodOptions,
    ProductTypeOptions,
    ProductFormOptions,
    SaveModeOptions,
} from "../../tools/mapper";

export default {
    mixins: [toastMixin],
    components: {
        vosOss,
        GoodsDetail,
        ShortCodeMatch,
        Add,
        VhSelect,
    },
    data() {
        return {
            dir: "vinehoo/wiki/supplier/",
            addVisible: false,
            goodsDetailVisible: false,
            shortCodeMatchVisible: false,
            rowData: {},
            file_list: [],
            RelatedStatusOptions,
            PackFoodOptions,
            ProductTypeOptions,
            ProductFormOptions,
            SaveModeOptions,
            productCategoryOptions: [],
            time1: "",
            time2: "",
            query: {
                product_category: this.supplierFoodNum,
                id: "",
                related_status: "",
                product_name: "",
                supplier: "",
                shelf_life_gt: "",
                shelf_life_lt: "",
                product_type: "",
                producing_area_cn: "",
                product_form: "",
                storage_methods: "",
                producer: "",
                pack: "",
                short_code: "",
                sort: "",
                page: 1,
                limit: 10,
            },
            total: 0,
            tableData: [],
            loading: false,
            goodsOptions: [],
            productOptions: [],
            sortOptions: [
                {
                    value: 3,
                    label: "库存升序",
                },
                {
                    value: 4,
                    label: "库存降序",
                },
                {
                    value: 5,
                    label: "报价升序",
                },
                {
                    value: 6,
                    label: "报价降序",
                },
                // {
                //     value: 7,
                //     label: "报价时间升序",
                // },
                // {
                //     value: 8,
                //     label: "报价时间降序",
                // },
            ],
        };
    },
    computed: {
        selectionAll({ tableData }) {
            return (
                tableData.length &&
                tableData
                    .filter((item) => item.product_id)
                    .every((item) => item.$selection)
            );
        },
    },
    mounted() {
        this.productCategory();
        this.supplierproductList();
    },
    methods: {
        //供应商商品列表
        async supplierproductList() {
            let data = {
                ...this.query,
            };
            // data.product_name = data.product_name
            //     ? data.product_name.join("&*#@")
            //     : "";
            let res = await this.$request.supplier.supplierproductList(data);
            if (res?.data?.error_code == 0) {
                const handleList = res?.data?.data?.list || [];
                handleList.forEach((item) => (item.$selection = false));
                this.tableData = handleList;
                this.total = res.data.data.total;
                this.batchLastSellTime();
            }
        },
        batchLastSellTime() {
            const filterList = this.tableData.filter((item) => item.short_code);
            if (filterList.length) {
                console.log("filterList", filterList);
                const short_codes = filterList
                    .map((item) => item.short_code)
                    .join(",");
                console.log(short_codes);
                this.$request.supplier
                    .batchLastSellTime({ short_codes })
                    .then((res) => {
                        if (res?.data?.error_code === 0) {
                            const openTimeInfo = res?.data?.data?.list || {};
                            const openTimeInfoKeys = Object.keys(openTimeInfo);
                            console.log(openTimeInfo);
                            this.tableData.forEach((item) => {
                                openTimeInfoKeys.forEach((item1) => {
                                    if (item.short_code === item1)
                                        item.$open_time = openTimeInfo[item1];
                                });
                            });
                        }
                    });
            }
        },
        search() {
            this.query.product_name = this.$refs.vhSelec1.name;
            this.query.page = 1;
            this.supplierproductList();
        },
        reset() {
            this.$refs.vhSelec1.name = "";
            this.query = this.$options.data().query;
            this.query.product_category = this.supplierFoodNum;
            this.time1 = "";
            this.time2 = "";
        },
        down() {
            window.location.href =
                "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/wiki/supplier/%E9%A3%9F%E5%93%81%E7%B1%BB%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx";
        },
        //供应商产品类别
        async productCategory() {
            let res = await this.$request.supplier.productCategory();
            if (res.data.error_code == 0) {
                this.productCategoryOptions = res.data.data;
            }
        },
        formatChange(time, startName, endName) {
            if (time) {
                this.query[startName] = time[0];
                this.query[endName] = time[1];
            } else {
                this.query[startName] = "";
                this.query[endName] = "";
            }
        },
        goodsDetail(row) {
            this.rowData = row;
            this.goodsDetailVisible = true;
        },
        shortCodeMatch(row) {
            this.rowData = row;
            this.shortCodeMatchVisible = true;
        },
        async handleSuccess(file) {
            console.log("批量导入", file);
            let data = {
                product_category: this.supplierFoodNum,
                file: file.file,
            };
            let res = await this.$request.supplier.supplierproductImpport(data);
            if (res.data.error_code == 0) {
                this.$message.success(`${res.data.error_msg}`);
                this.supplierproductList();
            }
            this.file_list = [];
            this.$refs.vos.handleviewFileList([]);
        },
        goodsNameRemoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.supplier
                    .searchGetId({ product_name: query })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.goodsOptions = res.data.data?.list;
                        }
                    });
            } else {
                this.goodsOptions = [];
            }
        },
        productRemoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.supplier
                    .searchGetId({ producing_area: query })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.productOptions = res.data.data?.list;
                        }
                    });
            } else {
                this.productOptions = [];
            }
        },
        handleSelectionAll() {
            const selectionAll = this.tableData
                .filter((item) => item.product_id)
                .every((item) => item.$selection);
            console.log(selectionAll);
            this.tableData
                .filter((item) => item.product_id)
                .forEach((item) => (item.$selection = !selectionAll));
        },
        openingForSale() {
            const multipleSelection = this.tableData.filter(
                (item) => item.$selection
            );
            if (!multipleSelection.length)
                return this.toast("error", "请先勾开售项！");
            console.log(multipleSelection);
            const products = multipleSelection.map(
                ({ id: sku_id, supplier, supplier_id, product_id }) => ({
                    sku_id,
                    supplier,
                    supplier_id,
                    product_id,
                })
            );
            this.$request.supplier.onSale({ products }).then((res) => {
                if (res?.data?.error_code === 0) {
                    this.toast("success", "开售成功");
                    this.supplierproductList();
                }
            });
            console.log(products);
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.supplierproductList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.query.page = val;
            this.supplierproductList();
        },
    },
};
</script>

<style lang="scss" scoped>
.f_box {
    display: flex;
    justify-content: left;
    flex-wrap: wrap;
}
</style>

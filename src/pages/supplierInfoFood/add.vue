<template>
    <div>
        <el-dialog
            title="新增供应商商品信息"
            :visible="visible"
            width="65%"
            :before-close="closeDialog"
            :close-on-click-modal="false"
        >
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                label-width="130px"
                class="demo-ruleForm"
                size="mini"
            >
                <div class="f_box">
                    <div>
                        <el-form-item label="英文品名" prop="en_product_name">
                            <el-input
                                v-model="ruleForm.en_product_name"
                                class="w-large"
                                placeholder="请输入英文品名"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="产品类型" prop="product_type">
                            <el-select
                                v-model="ruleForm.product_type"
                                filterable
                                placeholder="请选择产品类型"
                            >
                                <el-option
                                    v-for="item in ProductTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品单位" prop="pack">
                            <el-select
                                v-model="ruleForm.pack"
                                filterable
                                placeholder="请选择产品单位"
                            >
                                <el-option
                                    v-for="item in PackWineOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="供应商报价" prop="price">
                            <el-input
                                v-model="ruleForm.price"
                                placeholder="请输入供应商报价"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="国家（英文）" prop="en_country">
                            <el-input
                                v-model="ruleForm.en_country"
                                placeholder="请输入国家（英文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="生产商" prop="producer">
                            <el-input
                                v-model="ruleForm.producer"
                                placeholder="请输入生产商"
                                class="w-large"
                            ></el-input>
                        </el-form-item>

                        <el-form-item label="供应商" prop="supplier_id">
                            <!-- <el-select
                                v-model="ruleForm.supplier_id"
                                filterable
                                placeholder="请选择供应商"
                            >
                                <el-option
                                    v-for="item in supplierListOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select> -->
                            <el-select
                                v-model="ruleForm.supplier_id"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入供应商"
                                :remote-method="remoteMethod"
                                :loading="loading"
                                size="mini"
                                class="m-r-10"
                                clearable
                            >
                                <el-option
                                    v-for="item in supplierListOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="进口类型" prop="import_type">
                            <el-select
                                v-model="ruleForm.import_type"
                                filterable
                                placeholder="请选择进口类型"
                            >
                                <el-option
                                    v-for="item in ImportTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="生产日期" prop="produce_time">
                            <el-date-picker
                                v-model="ruleForm.produce_time"
                                type="date"
                                align="right"
                                size="mini"
                                value-format="yyyy-MM-dd"
                                placeholder="选择生产日期"
                                class="m-r-10"
                                style="width: 100%"
                                :picker-options="pickerOptions"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="中文品名" prop="cn_product_name">
                            <el-input
                                v-model="ruleForm.cn_product_name"
                                placeholder="请输入中文品名"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="产品形态" prop="product_form">
                            <el-select
                                v-model="ruleForm.product_form"
                                filterable
                                placeholder="请选择产品形态"
                            >
                                <el-option
                                    v-for="item in ProductFormOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="规格" prop="specs">
                            <el-input
                                v-model="ruleForm.specs"
                                placeholder="请输入规格"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="供应商库存" prop="stock">
                            <el-input-number
                                v-model="ruleForm.stock"
                                :controls="false"
                                :precision="0"
                                :min="0"
                                placeholder="请输入供应商库存"
                            ></el-input-number>
                            <!-- <el-input
                                v-model="ruleForm.stock"
                                placeholder="请输入供应商库存"
                            ></el-input> -->
                        </el-form-item>
                        <el-form-item label="国家（中文）" prop="cn_country">
                            <el-input
                                v-model="ruleForm.cn_country"
                                placeholder="请输入国家（中文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="生产地址" prop="producing_area_cn">
                            <el-input
                                v-model="ruleForm.producing_area_cn"
                                placeholder="请输入生产地址"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="存储方式" prop="storage_methods">
                            <el-select
                                v-model="ruleForm.storage_methods"
                                filterable
                                placeholder="请选择存储方式"
                            >
                                <el-option
                                    v-for="item in SaveModeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="保质期" prop="shelf_life">
                            <!-- <el-input
                                v-model="ruleForm.shelf_life"
                                placeholder="请输入保质期"
                            ></el-input> -->
                            <el-input-number
                                v-model="ruleForm.shelf_life"
                                :controls="false"
                                :precision="0"
                                :min="0"
                                placeholder="请输入保质期"
                            ></el-input-number>
                        </el-form-item>
                    </div>
                </div>

                <el-form-item style="text-align: center">
                    <el-button @click="closeDialog">取消</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm')"
                        >确定</el-button
                    >
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import dialogmixin from "../../mixins/dialogmixin";
import {
    ColorOptions,
    FoamingOptions,
    IntensifyTypeOptions,
    BoxGaugePackageOptions,
    SweetStandOptions,
    SweetFoamingOptions,
    PackWineOptions,
    ImportTypeOptions,
    ProductTypeOptions,
    ProductFormOptions,
    SaveModeOptions,
} from "../../tools/mapper";
export default {
    props: ["type"],
    mixins: [dialogmixin],
    data() {
        return {
            pickerOptions: {
                //返回true不可选，返回false可选
                disabledDate: (time) => {
                    return time.getTime() > Date.now();
                },
            },
            ColorOptions, //颜色
            FoamingOptions,
            IntensifyTypeOptions,
            BoxGaugePackageOptions,
            SweetStandOptions,
            SweetFoamingOptions,
            PackWineOptions,
            ImportTypeOptions,
            ProductTypeOptions,
            ProductFormOptions,
            SaveModeOptions,
            sweetOptions: [],
            supplierListOptions: [],
            loading: false,
            ruleForm: {
                en_product_name: "",
                cn_product_name: "",
                price: "",
                en_country: "",
                cn_country: "",
                supplier_id: "",
                producing_area_cn: "",
                specs: "",
                stock: "",
                product_type: "",
                pack: "",
                product_form: "",
                producer: "",
                shelf_life: "",
                import_type: "",
                storage_methods: "",
                produce_time: "",
            },
            rules: {
                import_type: [
                    {
                        required: true,
                        message: "请选择进口类型",
                        trigger: "change",
                    },
                ],
                product_type: [
                    {
                        required: true,
                        message: "请选择产品类型",
                        trigger: "change",
                    },
                ],
                // producer: [
                //     {
                //         required: true,
                //         message: "请输入生产商",
                //         trigger: "blur",
                //     },
                // ],
                product_form: [
                    {
                        required: true,
                        message: "请选择产品形态",
                        trigger: "change",
                    },
                ],
                // stock: [
                //     {
                //         required: true,
                //         message: "请输入供应商库存",
                //         trigger: "blur",
                //     },
                // ],
                shelf_life: [
                    {
                        required: true,
                        message: "请输入保质期",
                        trigger: "blur",
                    },
                ],
                pack: [
                    {
                        required: true,
                        message: "请选择单位",
                        trigger: "change",
                    },
                ],
                supplier_id: [
                    {
                        required: true,
                        message: "请选择供应商",
                        trigger: "change",
                    },
                ],
                cn_product_name: [
                    {
                        required: true,
                        message: "请输入中文品名",
                        trigger: "blur",
                    },
                ],
                en_product_name: [
                    {
                        required: true,
                        message: "请输入英文品名",
                        trigger: "blur",
                    },
                ],
                specs: [
                    {
                        required: true,
                        message: "请输入规格",
                        trigger: "blur",
                    },
                ],
                price: [
                    {
                        required: true,
                        message: "请输入供应商报价",
                        trigger: "blur",
                    },
                ],
                cn_country: [
                    {
                        required: true,
                        message: "请输入国家中文",
                        trigger: "blur",
                    },
                ],
                en_country: [
                    {
                        required: true,
                        message: "请输入国家英文",
                        trigger: "blur",
                    },
                ],
                // producing_area_cn: [
                //     {
                //         required: true,
                //         message: "请输入生产地址",
                //         trigger: "blur",
                //     },
                // ],
            },
        };
    },
    watch: {
        visible(val) {
            if (val) {
                this.ruleForm = this.$options.data().ruleForm;
                this.$nextTick(() => {
                    this.$refs.ruleForm.resetFields();
                });
                // this.supplierList();
            }
        },
    },
    methods: {
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                //  supplierList
                this.$request.supplier
                    .allListSupplier({ page: 1, limit: 50, name: query })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.supplierListOptions = res.data.data.list;
                        }
                        this.loading = false;
                    });
            } else {
                this.supplierListOptions = [];
            }
        },
        //供应商列表
        async supplierList() {
            //  supplierList
            let res = await this.$request.supplier.allListSupplier();
            if (res.data.error_code == 0) {
                this.supplierListOptions = res.data.data.list;
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        ...this.ruleForm,
                        product_category: this.type,
                    };
                    // data.stock = Number(data.stock);
                    data.supplier = this.supplierListOptions.find(
                        (item) => item.id == this.ruleForm.supplier_id
                    ).name;
                    this.$request.supplier.addProduct(data).then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("添加成功");
                            this.closeDialog();
                        }
                    });
                } else {
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.f_box {
    display: flex;
    justify-content: space-between;
}
</style>

<template>
    <div>
        <el-dialog
            title="标准商品信息"
            :visible="visible"
            width="60%"
            :before-close="closeDialog"
            :close-on-click-modal="false"
        >
            <el-form
                :model="ruleForm"
                :inline="true"
                ref="ruleForm"
                label-width="140px"
                class="demo-ruleForm"
                size="mini"
            >
                <div class="deliver_container">
                    <div class="deliver_content">
                        <span class="deliver_text">基本信息</span>
                        <el-divider></el-divider>
                    </div>
                </div>
                <el-form-item
                    label="渠道"
                    v-if="ruleForm.channel"
                    prop="channel"
                >
                    <el-select
                        v-model="ruleForm.channel"
                        filterable
                        placeholder="请选择渠道"
                        disabled
                    >
                        <el-option
                            v-for="item in channelList"
                            :key="item.value"
                            :label="item.name"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="商家"
                    v-if="ruleForm.merchants"
                    prop="merchants"
                >
                    <el-select
                        v-model="ruleForm.merchants"
                        filterable
                        multiple
                        reserve-keyword
                        placeholder="请选择商家"
                        :loading="loading"
                        disabled
                    >
                        <el-option
                            v-for="item in merchantsList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="中文品名"
                    v-if="ruleForm.cn_product_name"
                    prop="cn_product_name"
                >
                    <el-input
                        class="w-280"
                        v-model="ruleForm.cn_product_name"
                        disabled
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="英文品名"
                    v-if="ruleForm.en_product_name"
                    prop="en_product_name"
                >
                    <el-input
                        class="w-280"
                        v-model="ruleForm.en_product_name"
                        disabled
                    ></el-input>
                </el-form-item>

                <el-form-item
                    label="简码"
                    v-if="ruleForm.short_code"
                    prop="short_code"
                >
                    <!-- :onkeyup="
                            (ruleForm.short_code = ruleForm.short_code.replace(
                                /\s+/g,
                                ''
                            ))
                        " -->
                    <el-input
                        class="w-280"
                        v-model.trim="ruleForm.short_code"
                        disabled
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="葡萄采摘年份"
                    v-if="ruleForm.grape_picking_years"
                    prop="grape_picking_years"
                >
                    <el-date-picker
                        value-format="yyyy"
                        v-model="ruleForm.grape_picking_years"
                        type="year"
                        placeholder="不填默认为NV"
                        disabled
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item
                    label="产品类型"
                    v-if="ruleForm.product_type"
                    prop="product_type"
                >
                    <el-select
                        v-model="ruleForm.product_type"
                        filterable
                        placeholder="请选择"
                        disabled
                    >
                        <el-option
                            v-for="item in typeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>

                    <!-- @change="changeBarcode" -->
                    <!-- <el-cascader
                        :options="typeList"
                        v-model="ruleForm.product_type"
                        filterable
                        :show-all-levels="false"
                        :props="{
                            label: 'name',
                            value: 'id',
                            checkStrictly: true,
                        }"
                        disabled
                    >
                    </el-cascader> -->
                </el-form-item>
                <el-form-item
                    label="产品单位"
                    v-if="ruleForm.product_unit"
                    prop="product_unit"
                >
                    <el-select
                        v-model="ruleForm.product_unit"
                        filterable
                        placeholder="请选择"
                        disabled
                    >
                        <el-option
                            v-for="item in unitList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item
                    label="产品形态"
                    v-if="ruleForm.product_form"
                    prop="product_form"
                >
                    <el-radio-group
                        v-model="ruleForm.product_form"
                        style="width: 200px"
                    >
                        <el-radio
                            disabled
                            v-for="(item, index) in product_form"
                            :key="index"
                            :label="item.value"
                            >{{ item.name }}</el-radio
                        >
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    label="规格"
                    v-if="ruleForm.capacity"
                    prop="capacity"
                >
                    <el-input-number
                        v-model="ruleForm.capacity"
                        :min="0"
                        :precision="3"
                        label="请填写规格"
                        disabled
                    ></el-input-number>
                    {{ ruleForm.product_form | capacityType }}
                </el-form-item>
                <el-form-item
                    v-if="ruleForm.bar_code"
                    label="条码"
                    prop="bar_code"
                >
                    <el-input
                        disabled
                        class="w-280"
                        v-model.trim="ruleForm.bar_code"
                    ></el-input>
                    <el-button disabled style="margin-left: 5px" type="primary"
                        >生成条码</el-button
                    >
                </el-form-item>
                <el-form-item
                    label="成本"
                    v-if="ruleForm.costprice"
                    prop="costprice"
                >
                    <el-input-number
                        :min="0"
                        :precision="2"
                        v-model="ruleForm.costprice"
                        disabled
                    ></el-input-number>
                </el-form-item>
                <el-form-item
                    label="整箱成本"
                    v-if="ruleForm.cost_container"
                    prop="cost_container"
                >
                    <el-input-number
                        :min="0"
                        :precision="2"
                        v-model="ruleForm.cost_container"
                        disabled
                    ></el-input-number>
                </el-form-item>
                <el-form-item
                    label="税率"
                    v-if="ruleForm.tax_rate"
                    prop="tax_rate"
                >
                    <el-input-number
                        :min="0"
                        :max="0.99"
                        :precision="2"
                        v-model="ruleForm.tax_rate"
                        disabled
                    ></el-input-number>
                </el-form-item>

                <el-form-item
                    label="灌装日期"
                    v-if="ruleForm.canning_years"
                    prop="canning_years"
                >
                    <el-date-picker
                        value-format="yyyy-MM-dd"
                        v-model="ruleForm.canning_years"
                        type="date"
                        placeholder="选择灌装日期"
                        disabled
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item
                    v-if="ruleForm.shelf_life"
                    label="保质期"
                    prop="shelf_life"
                >
                    <el-input-number
                        v-model="ruleForm.shelf_life"
                        :min="0"
                        :precision="0"
                        :max="100000"
                        label="保质期（天）"
                        disabled
                    ></el-input-number>
                    天
                </el-form-item>
                <el-form-item
                    v-if="ruleForm.carton_dimension"
                    label="箱规"
                    prop="carton_dimension"
                >
                    <el-input-number
                        v-model="ruleForm.carton_dimension"
                        :min="0"
                        :max="100"
                        label="箱规"
                        :precision="0"
                        disabled
                    ></el-input-number>
                    瓶
                </el-form-item>
                <el-form-item label="重量" v-if="ruleForm.weight" prop="weight">
                    <el-input-number
                        v-model="ruleForm.weight"
                        :min="0"
                        :precision="3"
                        label="重量（kg）"
                        disabled
                    ></el-input-number>
                    千克
                </el-form-item>

                <!-- <el-form-item
                    v-if="ruleForm.is_gift"
                    label="是否赠品"
                    
                    prop="is_gift"
                >
                    <el-switch
                        v-model="ruleForm.is_gift"
                        active-color="#13ce66"
                        inactive-color="#999"
                    >
                    </el-switch>
                </el-form-item> -->
                <!-- <el-form-item
                    v-if="ruleForm.is_addition"
                    label="是否为附加物"
                    
                    prop="is_addition"
                >
                    <el-switch
                        v-model="ruleForm.is_addition"
                        active-color="#13ce66"
                        inactive-color="#999"
                        :active-value="1"
                        :inactive-value="0"
                    >
                    </el-switch>
                </el-form-item> -->
                <el-form-item
                    v-if="ruleForm.is_addition"
                    label="指令商品"
                    prop="is_addition"
                >
                    <el-switch
                        v-model="ruleForm.is_addition"
                        active-color="#13ce66"
                        inactive-color="#999"
                        :active-value="1"
                        :inactive-value="0"
                        disabled
                    >
                    </el-switch>
                </el-form-item>
                <el-form-item
                    label="长"
                    v-if="ruleForm.product_length"
                    prop="length"
                >
                    <el-input-number
                        v-model="ruleForm.product_length"
                        :min="0"
                        :max="99999999"
                        :precision="2"
                        label="长"
                        disabled
                    ></el-input-number>
                    厘米
                </el-form-item>
                <el-form-item
                    label="宽"
                    v-if="ruleForm.product_width"
                    prop="width"
                >
                    <el-input-number
                        v-model="ruleForm.product_width"
                        :min="0"
                        :max="99999999"
                        :precision="2"
                        label="宽"
                        disabled
                    ></el-input-number>
                    厘米
                </el-form-item>
                <el-form-item
                    label="高"
                    v-if="ruleForm.product_height"
                    prop="high"
                >
                    <el-input-number
                        v-model="ruleForm.product_height"
                        :min="0"
                        :max="99999999"
                        :precision="2"
                        label="高"
                        disabled
                    ></el-input-number>
                    厘米
                </el-form-item>
                <el-form-item
                    label="是否同步T+"
                    v-if="ruleForm.issync"
                    prop="issync"
                >
                    <el-select
                        v-model="ruleForm.issync"
                        filterable
                        placeholder="请选择"
                        disabled
                    >
                        <el-option
                            v-for="item in issyncList"
                            :key="item.value"
                            :label="item.name"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item
                    label="仓库"
                    v-if="ruleForm.store_code"
                    prop="store_code"
                >
                    <el-select
                        v-model="ruleForm.store_code"
                        filterable
                        placeholder="请选择仓库"
                        multiple
                    >
                        <el-option
                            v-for="item in storeCodeList"
                            :key="item.store_code"
                            :label="item.store_name"
                            :value="item.store_code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item
                    label="京东ECLP商品编号"
                    v-if="ruleForm.jd_eclp_code"
                >
                    <el-input
                        v-model="ruleForm.jd_eclp_code"
                        placeholder="请输入京东ECLP商品编号"
                        clearable
                        disabled
                    ></el-input>
                </el-form-item>

                <div class="expansion_info">
                    <div class="deliver_container">
                        <div class="deliver_content">
                            <span class="deliver_text">拓展信息</span>
                            <el-divider></el-divider>
                        </div>
                    </div>
                    <el-form-item
                        label="国家"
                        v-if="ruleForm.country_id"
                        prop="country_id"
                    >
                        <el-select
                            v-model="ruleForm.country_id"
                            filterable
                            style="width: 280px"
                            remote
                            clearabl
                            reserve-keyword
                            placeholder="请输入国家"
                            disabled
                        >
                            <el-option
                                v-for="item in CountryOptions"
                                :key="item.id"
                                :label="
                                    item.country_name_cn + item.country_name_en
                                "
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        v-if="ruleForm.producing_area_id"
                        label="产区"
                        prop="producing_area_id"
                    >
                        <el-select
                            v-model="ruleForm.producing_area_id"
                            filterable
                            remote
                            style="width: 280px"
                            clearable
                            reserve-keyword
                            placeholder="请输入产区"
                            :remote-method="getRegionList"
                            :loading="loading"
                            disabled
                        >
                            <el-option
                                v-for="item in RegionOptions"
                                :key="item.id"
                                :label="
                                    item.regions_name_cn + item.regions_name_en
                                "
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="酒庄"
                        v-if="ruleForm.chateau_id"
                        prop="chateau_id"
                    >
                        <el-select
                            v-model="ruleForm.chateau_id"
                            filterable
                            style="width: 280px"
                            clearable
                            remote
                            reserve-keyword
                            placeholder="请输入酒庄"
                            :remote-method="getWineryList"
                            :loading="loading"
                            disabled
                        >
                            <el-option
                                v-for="item in WineryOptions"
                                :key="item.id"
                                :label="
                                    item.winery_name_cn + item.winery_name_en
                                "
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <!-- <div v-if="this.typeRadio == 1"> -->
                    <el-form-item
                        label="葡萄品种"
                        prop="grape"
                        v-if="ruleForm.grape_collection"
                    >
                        <el-select
                            style="width: 280px"
                            v-model="ruleForm.grape_collection"
                            filterable
                            remote
                            multiple
                            reserve-keyword
                            placeholder="请输入葡萄品种"
                            :remote-method="getGrapeList"
                            :loading="loading"
                            disabled
                        >
                            <el-option
                                v-for="item in GrapeOptions"
                                :key="item.id"
                                :label="item.gname_cn + item.gname_en"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="残糖"
                        v-if="ruleForm.residual_sugar"
                        prop="residual_sugar"
                    >
                        <el-input-number
                            :precision="2"
                            v-model="ruleForm.residual_sugar"
                            label="残糖"
                            disabled
                        ></el-input-number>
                        g/L
                        <!-- <el-input
                            v-model="ruleForm.residual_sugar"
                            label="残糖"
                        ></el-input> -->
                    </el-form-item>
                    <el-form-item
                        label="酒精度"
                        v-if="ruleForm.alcohol"
                        prop="alcohol"
                    >
                        <el-input-number
                            v-model="ruleForm.alcohol"
                            :min="0"
                            :max="100"
                            :precision="2"
                            label="酒精度"
                            disabled
                        ></el-input-number>
                        %vol
                        <!-- <el-input
                            v-model="ruleForm.alcohol"
                            label="酒精度"
                        ></el-input> -->
                    </el-form-item>
                    <el-form-item
                        label="关键词"
                        prop="grape"
                        v-if="ruleForm.product_keywords_id"
                    >
                        <el-select
                            v-model="ruleForm.product_keywords_id"
                            filterable
                            remote
                            multiple
                            reserve-keyword
                            placeholder="请输入关键词"
                            disabled
                        >
                            <el-option
                                v-for="item in keywordList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="酿造工艺"
                        v-if="ruleForm.brewing"
                        prop="tasting_notes"
                    >
                        <el-input
                            class="w-280"
                            v-model="ruleForm.brewing"
                            disabled
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="tasting_notes"
                        v-if="ruleForm.tasting_notes"
                        prop="tasting_notes"
                    >
                        <el-input
                            type="textarea"
                            class="w-330"
                            :autosize="{ minRows: 2 }"
                            v-model="ruleForm.tasting_notes"
                            disabled
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="评分"
                        v-if="ruleForm.score"
                        prop="score"
                    >
                        <el-input
                            class="w-330"
                            :autosize="{ minRows: 2 }"
                            type="textarea"
                            v-model="ruleForm.score"
                            disabled
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="获奖"
                        v-if="ruleForm.prize"
                        prop="prize"
                    >
                        <el-input
                            class="w-330"
                            :autosize="{ minRows: 2 }"
                            type="textarea"
                            v-model="ruleForm.prize"
                            disabled
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="饮用建议"
                        v-if="ruleForm.drinking_suggestion"
                        prop="drinking_suggestion"
                    >
                        <el-input
                            class="w-330"
                            :autosize="{ minRows: 2 }"
                            type="textarea"
                            v-model="ruleForm.drinking_suggestion"
                            disabled
                        ></el-input>
                    </el-form-item>
                    <!-- </div> -->
                </div>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import dialogmixin from "../../mixins/dialogmixin";
export default {
    mixins: [dialogmixin],
    props: ["type", "rowData"],
    data() {
        return {
            unitList: [], //单位
            channelList: [], //渠道
            typeList: [], //产品类别
            issyncList: [], //是否同步选项
            CountryOptions: [], //国家
            keywordList: [], //关键词
            RegionOptions: [], //产区
            WineryOptions: [], //酒庄
            product_form: [], //产品形态
            merchantsList: [], //商家
            GrapeOptions: [], //葡萄品种
            loading: false,
            ruleForm: {},
            rules: {},
        };
    },
    filters: {
        capacityType(val) {
            if (val) {
                return "千克";
            } else {
                return "毫升";
            }
        },
    },
    watch: {
        visible(val) {
            if (val) {
                this.productType();
                this.getPropertyList();
                this.getCountry();
                this.getKeywrodList();
                this.getMerchant();
                this.productDetail();
            }
        },
    },
    methods: {
        // 产品类别
        async productType() {
            let res = await this.$request.product.getProductCategory({
                pid: this.rowData.type_id,
            });
            if (res.data.error_code == 0) {
                this.typeList = res.data.data.list;
            }
        },
        getMerchant(params) {
            this.$request.product
                .getMerchantList({
                    name: params || "",
                    shop_status: 1,
                })
                .then((res) => {
                    this.merchantsList = res.data.data.list;
                });
        },
        getCountry() {
            this.$request.product
                .getCountryList({
                    page: 1,
                    limit: 10,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.CountryOptions = res.data.data.list;
                    }
                });
        },
        getKeywrodList() {
            this.$request.product.getKeyword().then((res) => {
                this.keywordList = res.data.data.list;
            });
        },
        getPropertyList() {
            // 获取产品类型字段列表
            let data = {
                type_id: this.rowData.type_id,
            };
            this.$request.product.getPropertyList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.rules = res.data.data.list;
                    if (this.rules.channel) {
                        this.channelList = this.rules.channel[0].select;
                        // if (!this.ruleForm.channel) {
                        //     this.ruleForm.channel = Number(
                        //         this.channelList[0].value
                        //     );
                        // }
                    }
                    if (this.rules.product_unit) {
                        this.unitList = this.rules.product_unit[0].list;
                        // if (!this.ruleForm.product_unit) {
                        //     this.ruleForm.product_unit = this.unitList[0].id;
                        // }
                    }
                    if (this.rules.issync) {
                        this.issyncList = this.rules.issync[0].select;
                        // if (!this.ruleForm.issync) {
                        //     this.ruleForm.issync = this.issyncList[1].value;
                        // }
                    }
                    if (this.rules.product_form) {
                        this.product_form = this.rules.product_form[0].select;
                        // if (!this.ruleForm.product_form) {
                        //     this.ruleForm.product_form =
                        //         this.product_form[0].value;
                        // }
                    }
                }
            });
        },
        //产区
        getRegionList(keyword) {
            if (keyword) {
                if (this.ruleForm.producing_area_id) {
                    this.ruleForm.producing_area_id = parseInt(
                        this.ruleForm.producing_area_id
                    );
                }

                this.$request.product
                    .getRegionList({
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.RegionOptions = res.data.data.list;
                        }
                    });
            }
        },
        //酒庄
        getWineryList(keyword) {
            if (keyword) {
                if (this.ruleForm.chateau_id) {
                    this.ruleForm.chateau_id = parseInt(
                        this.ruleForm.chateau_id
                    );
                }
                this.$request.product
                    .getWineryList({
                        keyword,
                        page: 1,
                        limit: 10,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.WineryOptions = res.data.data.list;
                        }
                    });
            }
        },
        getGrapeList(keyword) {
            if (keyword) {
                this.$request.product
                    .getGrapeList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.GrapeOptions = res.data.data.list;
                        }
                    });
            }
        },
        async productDetail() {
            let res = await this.$request.supplier.productDetail({
                id: this.rowData.product_id,
            });
            if (res.data.error_code == 0) {
                this.ruleForm = res.data.data;
                this.ruleForm.issync = String(this.ruleForm.issync);
                this.getRegionList(this.ruleForm.producing_area_id);
                this.getWineryList(this.ruleForm.chateau_id);
                this.getGrapeList(this.ruleForm.grape_collection);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
/deep/ .el-form-item {
    width: 48%;
}
>>> .el-message-box__wrapper
    .el-message-box
    .el-message-box__container
    .el-message-box__message
    p {
    word-break: break-all !important;
}
.avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    border: 1px dashed #d9d9d9;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    object-fit: cover;
    width: 100%;
    display: block;
}
/deep/ .w-280 {
    width: 280px !important;
}
/deep/ .w-330 {
    width: 330px !important;
}
.box-card {
    width: 200px;
}
.main-form {
    margin-top: 20px;
}
.step1 {
    text-align: center;
}
.grape_name {
    width: 80px;
}
.deliver_container {
    display: flex;
    align-items: center;
    justify-content: center;
}
.deliver_content {
    display: flex;
    align-items: center;
    width: 90%;
    justify-content: center;
    margin: 5px 0 20px 0;
    .deliver_text {
        width: 100px;
        font-size: 16px;
        font-weight: 600;
    }
}
</style>

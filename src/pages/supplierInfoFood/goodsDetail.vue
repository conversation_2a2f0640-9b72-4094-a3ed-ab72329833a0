<template>
    <div>
        <el-dialog
            title="商品详情"
            :visible="visible"
            width="65%"
            :before-close="closeDialog"
            :close-on-click-modal="false"
        >
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                label-width="130px"
                class="demo-ruleForm"
                size="mini"
            >
                <el-divider content-position="left">基本信息</el-divider>
                <div class="f_box">
                    <div>
                        <el-form-item label="中文品名" prop="cn_product_name">
                            <el-input
                                v-model="ruleForm.cn_product_name"
                                placeholder="请输入中文品名"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="产品类型" prop="product_type">
                            <el-select
                                v-model="ruleForm.product_type"
                                filterable
                                placeholder="请选择产品类型"
                            >
                                <el-option
                                    v-for="item in ProductTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="规格" prop="specs">
                            <el-input
                                v-model="ruleForm.specs"
                                placeholder="请输入规格"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="保质期" prop="shelf_life">
                            <!-- <el-input
                                v-model="ruleForm.shelf_life"
                                placeholder="请输入保质期"
                            ></el-input> -->
                            <el-input-number
                                v-model="ruleForm.shelf_life"
                                :controls="false"
                                :precision="0"
                                :min="0"
                                placeholder="请输入保质期"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="生产日期" prop="produce_time">
                            <el-date-picker
                                v-model="ruleForm.produce_time"
                                type="date"
                                align="right"
                                size="mini"
                                value-format="yyyy-MM-dd"
                                placeholder="选择生产日期"
                                class="m-r-10"
                                style="width: 100%"
                                :picker-options="pickerOptions"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="产品单位" prop="pack">
                            <el-select
                                v-model="ruleForm.pack"
                                filterable
                                placeholder="请选择产品单位"
                            >
                                <el-option
                                    v-for="item in PackFoodOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div style="margin-left: 8%">
                        <el-form-item label="英文品名" prop="en_product_name">
                            <el-input
                                v-model="ruleForm.en_product_name"
                                class="w-large"
                                placeholder="请输入英文品名"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="产品形态" prop="product_form">
                            <el-select
                                v-model="ruleForm.product_form"
                                filterable
                                placeholder="请选择进口类型"
                            >
                                <el-option
                                    v-for="item in ProductFormOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="存储方式" prop="storage_methods">
                            <el-select
                                v-model="ruleForm.storage_methods"
                                filterable
                                placeholder="请选择存储方式"
                            >
                                <el-option
                                    v-for="item in SaveModeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="供应商提供库存" prop="stock">
                            <!-- <el-input
                                v-model="ruleForm.stock"
                                placeholder="请输入供应商提供库存"
                            ></el-input> -->
                            <el-input-number
                                v-model="ruleForm.stock"
                                :controls="false"
                                :precision="0"
                                placeholder="请输入供应商库存"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item
                            :label="`供应商报价 (${ruleForm.currency})`"
                            prop="price"
                        >
                            <el-input
                                v-model="ruleForm.price"
                                placeholder="请输入供应商报价"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="进口类型" prop="import_type">
                            <el-select
                                v-model="ruleForm.import_type"
                                filterable
                                placeholder="请选择进口类型"
                            >
                                <el-option
                                    v-for="item in ImportTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div class="m-l-20">
                        <el-link
                            type="primary"
                            :underline="false"
                            @click="standardGoodsInfoVisible = true"
                            v-if="rowData.short_code"
                            >绑定简码：{{ rowData.short_code }}</el-link
                        >
                    </div>
                </div>

                <el-divider content-position="left">拓展信息</el-divider>

                <div class="f_box">
                    <div style="width: 40%">
                        <el-form-item label="供应商" prop="supplier_id">
                            <!-- <el-select
                                v-model="ruleForm.supplier_id"
                                filterable
                                placeholder="请选择供应商"
                            >
                                <el-option
                                    v-for="item in supplierListOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select> -->
                            <el-select
                                v-model="ruleForm.supplier_id"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入供应商"
                                :remote-method="remoteMethod"
                                :loading="loading"
                                size="mini"
                                class="m-r-10"
                                clearable
                            >
                                <el-option
                                    v-for="item in supplierListOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="国家（中文）" prop="cn_country">
                            <el-input
                                v-model="ruleForm.cn_country"
                                placeholder="请输入国家（中文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>

                        <el-form-item label="生产地址" prop="producing_area_cn">
                            <el-input
                                v-model="ruleForm.producing_area_cn"
                                placeholder="请输入生产地址"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="正标" prop="frontal_label_img">
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :limit="1"
                                :dir="dir"
                                :file-list="frontal_label_img"
                                :disabled="true"
                                ref="vos1"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                        </el-form-item>
                        <el-form-item label="英文背标" prop="en_back_label_img">
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :limit="1"
                                :dir="dir"
                                :file-list="en_back_label_img"
                                :disabled="true"
                                ref="vos2"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                        </el-form-item>
                        <!-- <el-form-item label="产品图" prop="product_image">
                            <vos-oss
                                list-type="text"
                                :showFileList="true"
                                :limit="9"
                                :dir="dir"
                                :file-list="product_image"
                                ref="vos3"
                            >
                                <el-button size="mini" type="success"
                                    >上传附件</el-button
                                >
                            </vos-oss>
                        </el-form-item> -->
                    </div>
                    <div style="width: 50%; margin-left: 8%">
                        <el-form-item label="生产商" prop="producer">
                            <el-input
                                v-model="ruleForm.producer"
                                placeholder="请输入生产商"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="国家（英文）" prop="en_country">
                            <el-input
                                v-model="ruleForm.en_country"
                                placeholder="请输入国家（英文）"
                                class="w-large"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="中文背标" prop="cn_back_label_img">
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :limit="1"
                                :dir="dir"
                                :file-list="cn_back_label_img"
                                :disabled="true"
                                ref="vos4"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                        </el-form-item>
                        <el-form-item label="附件" prop="product_attachment">
                            <div class="w-max-large">
                                <el-link
                                    type="primary"
                                    :underline="false"
                                    v-for="(
                                        item, index
                                    ) in ruleForm.product_attachment"
                                    :key="index"
                                    @click="open(item)"
                                    >{{ item }}</el-link
                                >
                            </div>
                        </el-form-item>
                    </div>
                </div>

                <div style="text-align: center; margin-left: -130px">
                    <el-form-item>
                        <el-button @click="closeDialog">取消</el-button>
                        <el-button
                            type="primary"
                            @click="submitForm('ruleForm')"
                            >确定</el-button
                        >
                    </el-form-item>
                </div>
            </el-form>
        </el-dialog>
        <StandardGoodsInfo
            :visible.sync="standardGoodsInfoVisible"
            :rowData="ruleForm"
            :type="type"
        />
    </div>
</template>

<script>
import dialogmixin from "../../mixins/dialogmixin";
import vosOss from "vos-oss";
import StandardGoodsInfo from "./standardGoodsInfo.vue";
import {
    PackFoodOptions,
    ImportTypeOptions,
    ProductTypeOptions,
    ProductFormOptions,
    SaveModeOptions,
} from "../../tools/mapper";
export default {
    components: {
        vosOss,
        StandardGoodsInfo,
    },
    mixins: [dialogmixin],
    props: ["type", "rowData"],
    data() {
        return {
            pickerOptions: {
                //返回true不可选，返回false可选
                disabledDate: (time) => {
                    return time.getTime() > Date.now();
                },
            },
            dir: "vinehoo/wiki/supplier/",
            standardGoodsInfoVisible: false,
            supplierListOptions: [],
            productCategoryOptions: [],
            PackFoodOptions,
            ImportTypeOptions,
            ProductTypeOptions,
            ProductFormOptions,
            SaveModeOptions,
            frontal_label_img: [],
            en_back_label_img: [],
            // product_image: [],
            cn_back_label_img: [],
            loading: false,
            ruleForm: {
                cn_product_name: "",
                price: "",
                specs: "",
                en_product_name: "",
                stock: "",
                produce_time: "",
                supplier_id: "",
                cn_country: "",
                producing_area_cn: "",
                en_country: "",
                product_attachment: "",
                pack: "",
                import_type: "",
                product_type: "",
                shelf_life: "",
                product_form: "",
                storage_methods: "",
                producer: "",
            },
            rules: {
                cn_product_name: [
                    {
                        required: true,
                        message: "请输入中文品名",
                        trigger: "blur",
                    },
                ],
                price: [
                    {
                        required: true,
                        message: "请输入供应商报价",
                        trigger: "blur",
                    },
                ],
                specs: [
                    {
                        required: true,
                        message: "请输入规格",
                        trigger: "blur",
                    },
                ],
                en_product_name: [
                    {
                        required: true,
                        message: "请输入英文品名",
                        trigger: "blur",
                    },
                ],
                stock: [
                    {
                        required: true,
                        message: "请输入库存",
                        trigger: "blur",
                    },
                ],
                produce_time: [
                    {
                        required: true,
                        message: "请选择生产日期",
                        trigger: "blur",
                    },
                ],
                supplier_id: [
                    {
                        required: true,
                        message: "请选择供应商",
                        trigger: "change",
                    },
                ],
                cn_country: [
                    {
                        required: true,
                        message: "请输入国家（中文）",
                        trigger: "blur",
                    },
                ],
                producing_area_cn: [
                    {
                        required: true,
                        message: "请输入产区（中文）",
                        trigger: "blur",
                    },
                ],
                en_country: [
                    {
                        required: true,
                        message: "请输入国家（英文）",
                        trigger: "blur",
                    },
                ],
                pack: [
                    {
                        required: true,
                        message: "请选择单位",
                        trigger: "change",
                    },
                ],
                import_type: [
                    {
                        required: true,
                        message: "请选择进口类型",
                        trigger: "change",
                    },
                ],
                product_type: [
                    {
                        required: true,
                        message: "请选择产品类型",
                        trigger: "change",
                    },
                ],
                product_form: [
                    {
                        required: true,
                        message: "请选择产品形态",
                        trigger: "change",
                    },
                ],
                storage_methods: [
                    {
                        required: true,
                        message: "请选择存储方式",
                        trigger: "change",
                    },
                ],
                shelf_life: [
                    {
                        required: true,
                        message: "请输入保质期",
                        trigger: "blur",
                    },
                ],
                producer: [
                    {
                        required: true,
                        message: "请输入生产商",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    watch: {
        visible(val) {
            console.log(val, this.type);
            if (val) {
                this.ruleForm = this.$options.data().ruleForm;
                this.$nextTick(() => {
                    this.$refs.vos1.handleviewFileList([]);
                    this.$refs.vos2.handleviewFileList([]);
                    // this.$refs.vos3.handleviewFileList([]);
                    this.$refs.vos4.handleviewFileList([]);
                    this.frontal_label_img = [];
                    this.en_back_label_img = [];
                    // this.product_image = [];
                    this.cn_back_label_img = [];
                    this.$refs.ruleForm.resetFields();
                });
                // this.supplierList();
                this.productCategory();
                this.goodsDetail();
            }
        },
    },
    methods: {
        open(url) {
            window.open(url, "_blank");
        },
        async goodsDetail() {
            console.log(this.rowData);
            let res = await this.$request.supplier.goodsDetail({
                id: this.rowData.id,
            });
            if (res.data.error_code == 0) {
                this.ruleForm = res.data.data;
                this.frontal_label_img = res.data.data.frontal_label_img
                    ? res.data.data.frontal_label_img.split(",")
                    : [];
                this.en_back_label_img = res.data.data.en_back_label_img
                    ? res.data.data.en_back_label_img.split(",")
                    : [];
                // this.product_image = res.data.data.product_image
                //     ? res.data.data.product_image.split(",")
                //     : [];
                this.cn_back_label_img = res.data.data.cn_back_label_img
                    ? res.data.data.cn_back_label_img.split(",")
                    : [];
                this.ruleForm.product_attachment = res.data.data
                    .product_attachment
                    ? res.data.data.product_attachment.split(",")
                    : [];

                this.remoteMethod(this.ruleForm.supplier);
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let {
                        cn_product_name,
                        price,
                        specs,
                        en_product_name,
                        stock,
                        produce_time,
                        supplier_id,
                        cn_country,
                        producing_area_cn,
                        en_country,
                        product_attachment,
                        pack,
                        import_type,
                        product_type,
                        shelf_life,
                        product_form,
                        storage_methods,
                        producer,
                    } = this.ruleForm;
                    let data = {
                        id: this.rowData.id,
                        // product_image: this.product_image.join(","),
                        cn_product_name,
                        price,
                        specs,
                        en_product_name,
                        stock,
                        product_category: this.supplierFoodNum,
                        produce_time,
                        supplier_id,
                        cn_country,
                        producing_area_cn,
                        en_country,
                        product_attachment,
                        pack,
                        import_type,
                        product_type,
                        shelf_life,
                        product_form,
                        storage_methods,
                        producer,
                    };
                    data.supplier = this.supplierListOptions.find(
                        (item) => item.id == this.ruleForm.supplier_id
                    ).name;
                    this.$request.supplier
                        .goodsDetailUpdate(data)
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("修改成功");
                                this.closeDialog();
                            }
                        });
                } else {
                    return false;
                }
            });
        },
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                //  supplierList
                this.$request.supplier
                    .allListSupplier({ page: 1, limit: 50, name: query })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.supplierListOptions = res.data.data.list;
                        }
                        this.loading = false;
                    });
            } else {
                this.supplierListOptions = [];
            }
        },
        //供应商列表
        async supplierList() {
            //  supplierList
            let res = await this.$request.supplier.allListSupplier();
            if (res.data.error_code == 0) {
                this.supplierListOptions = res.data.data.list;
            }
        },
        //供应商产品类别
        async productCategory() {
            let res = await this.$request.supplier.productCategory();
            if (res.data.error_code == 0) {
                this.productCategoryOptions = res.data.data;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.f_box {
    display: flex;
    justify-content: left;
    flex-wrap: wrap;
}
.w-max-large {
    width: 100%;
}
</style>

<template>
    <!-- begin error -->

    <div class="error-layout">
        <div class="error" style="height: auto">
            <div class="error-code m-b-10">
                <transition name="fade" mode="out-in">
                    <div v-if="isLoading" class="loading-icon"></div>
                    <span v-else>{{ active.title }}</span>
                </transition>
            </div>
            <div class="error-content">
                <div class="error-message">{{ active.message }}</div>
                <div class="error-desc m-b-30">
                    <span v-html="active.desc"></span>
                </div>
            </div>
        </div>
    </div>
    <!-- end error -->
</template>
<script>
export default {
    data() {
        return {
            isLoading: true,
            notFound: {
                title: "404",
                message: "We couldn't find it...",
                desc: "The page you're looking for doesn't exist. <br /> Perhaps, there pages will help find what you're looking for.",
            },
            active: {
                title: "",
                message: "",
                desc: "",
            },
            loading: {
                message: "We Loading page it...",
                desc: "We are trying to loading the page, please wait.<br /> this process will not take too long.",
            },
        };
    },
    mounted() {
        this.active = this.loading;
        setTimeout(() => {
            this.isLoading = false;
            this.active = this.notFound;
        }, 1500);
    },
};
</script>
<style lang="scss" scoped>
.error-layout {
    height: 80vh;
    .loading {
        font-weight: 800;
        background-color: #000;
        color: #fff;
    }
}
.error-content {
    border-radius: 10px;
}
body {
    background: #222;
    color: #fff;
}
.loading-icon {
    padding: 10px;
    margin: auto 47%;
    width: 10px;
    height: 10px;
    border-top: 20px solid #ed5548;
    border-right: 20px solid #599cd3;
    border-bottom: 20px solid #5cbd5e;
    margin: 70px 0;
    border-left: 20px solid #fdd901;
    background: transparent;
    -webkit-animation: rotate-right-round 1200ms ease-in-out infinite alternate;
    -moz-animation: rotate-right-round 1200ms ease-in-out infinite alternate;
}
@-webkit-keyframes rotate-right-round {
    0% {
        -webkit-transform: rotate(0deg);
    }
    50% {
        -webkit-transform: rotate(180deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -webkit-border-radius: 50%;
    }
}
@-moz-keyframes rotate-right-round {
    0% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(180deg);
    }
    100% {
        transform: rotate(360deg);
        border-radius: 50%;
    }
}
h1 {
    text-align: center;
    margin: 5% 0;
    font-family: "Droid Sans", sans-serif;
    font-weight: normal;
    font-size: 50px;
} /* latin */
.fade-enter-active {
    transition: opacity 0.7s;
}
.fade-leave-active {
    transition: opacity 0.7s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
    opacity: 0;
}
</style>

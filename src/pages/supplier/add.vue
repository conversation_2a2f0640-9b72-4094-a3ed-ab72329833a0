<template>
    <el-form
        :model="supplierForm"
        ref="supplierForm"
        :rules="supplierFormrRules"
        label-width="160px"
        :inline="false"
    >
        <el-form-item label="供应商名称" prop="supplier_name">
            <el-input
                v-model="supplierForm.supplier_name"
                class="w-220"
            ></el-input>
        </el-form-item>
        <!-- corp -->
        <el-form-item label="收款公司主体" prop="corp">
            <el-select
                :value="supplierForm.corp ? supplierForm.corp.split(',') : []"
                placeholder="请选择收款公司主体"
                multiple
                class="w-220"
                @input="onCorpInput"
            >
                <el-option
                    v-for="item in corpType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                ></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="纳税号" prop="rax_no">
            <el-input v-model="supplierForm.rax_no" class="w-220"></el-input>
        </el-form-item>
        <el-form-item label="供应商负责人" prop="supplier_director">
            <el-input
                v-model="supplierForm.supplier_director"
                class="w-220"
            ></el-input>
        </el-form-item>
        <el-form-item label="供应商电话" prop="supplier_tel">
            <el-input
                v-model="supplierForm.supplier_tel"
                class="w-220"
            ></el-input>
        </el-form-item>
        <el-form-item label="下单税率" prop="supplier_tax">
            <el-input
                v-model="supplierForm.supplier_tax"
                placeholder="请输入0到1之间的两位小数"
                class="w-220"
                clearable
                @input="handleTaxInput"
            ></el-input>
        </el-form-item>
        <el-form-item label="合同起止日期">
            <el-date-picker
                v-model="contractTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
            />
        </el-form-item>
        <el-form-item label="签约类型">
            <el-radio-group v-model="supplierForm.sign_type">
                <el-radio :label="1" style="margin-bottom: 0">新签</el-radio>
                <el-radio :label="2" style="margin-bottom: 0">续签</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="转移时间">
            <el-date-picker
                v-model="supplierForm.transfer_time"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择转移时间"
            />
        </el-form-item>
        <el-form-item label="首次下单时间">
            <el-date-picker
                v-model="supplierForm.first_order_time"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择首次下单时间"
            />
        </el-form-item>
        <el-form-item label="备注">
            <el-input
                type="textarea"
                :autosize="{ minRows: 2 }"
                v-model="supplierForm.remark"
            />
        </el-form-item>
    </el-form>
</template>
<script>
export default {
    data() {
        // eslint-disable-next-line no-unused-vars
        const checkSupplierTel = (rules, value, callback) => {
            if (value) {
                if (!/^((0\d{2,3}-\d{7,8})|(1[357849]\d{9}))$/.test(value)) {
                    callback(new Error("请正确填写手机号码"));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };

        // 修改税率验证函数
        const checkTax = (rule, value, callback) => {
            if (!value) {
                callback();
            } else {
                const num = parseFloat(value);
                if (isNaN(num)) {
                    callback(new Error('请输入有效的数字'));
                } else if (num < 0 || num >= 1) {
                    callback(new Error('税率必须大于等于0且小于1'));
                } else {
                    callback();
                }
            }
        };

        return {
            supplierForm: {
                supplier_name: "",
                corp: "",
                rax_no: "",
                supplier_director: "",
                supplier_tel: "",
                sign_type: "",
                transfer_time: "",
                first_order_time: "",
                remark: "",
                supplier_tax:"",
            },
            supplierFormClone: {
                supplier_name: "",
                supplier_director: "",
                supplier_tel: "",
            },
            supplierFormrRules: {
                supplier_name: [
                    {
                        required: true,
                        message: "请输入供应商名称",
                        trigger: "blur",
                    },
                ],
                corp: [
                    {
                        required: true,
                        message: "请选择收款公司主体",
                        trigger: "change",
                    },
                ],
                rax_no: [
                    {
                        required: true,
                        message: "请输入纳税号",
                        trigger: "blur",
                    },
                ],
                supplier_tel: [
                    {
                        validator: checkSupplierTel,
                        trigger: "change",
                    },
                ],
                supplier_tax: [
                    { validator: checkTax, trigger: 'change' }
                ],
                isEdit: true,
            },
            //  001 科技 002 云酒
            corpType: [
                {
                    label: "佰酿云酒（重庆）科技有限公司",
                    value: "001",
                },
                {
                    label: "重庆云酒佰酿电子商务有限公司",
                    value: "002",
                },
                {
                    label: "渝中区微醺酒业商行",
                    value: "008",
                },
                { label: "海南一花一世界科技有限公司", value: '032'},
            ],
            contractTimeRange: "",
        };
    },

    mounted() {},

    methods: {
        // clearSupplier() {
        //     this.supplierForm = JSON.parse(
        //         JSON.stringify(this.supplierFormClone)
        //     );
        // },
        getEditDetails(val) {
            this.supplierForm = JSON.parse(JSON.stringify(val));
            if (this.supplierForm.supplier_tax === '') {
                this.supplierForm.supplier_tax = null;
            }
            const { contract_start, contract_end } = val;
            if (contract_start && contract_end) {
                this.contractTimeRange = [contract_start, contract_end];
            }
            this.isEdit = true;
        },
        submitSupplier() {
            this.$refs.supplierForm.validate((valid) => {
                if (valid) {
                    let submitMethod = this.isEdit
                        ? "updateSupplier"
                        : "addSupplier";
                    const [contract_start = "", contract_end = ""] =
                        this.contractTimeRange || [];
                    this.$request.supplier[submitMethod]({
                        ...this.supplierForm,
                        contract_start,
                        contract_end,
                    }).then((result) => {
                        if (result.data.error_code == 0) {
                            this.$emit("updateSupplier");
                        }
                    });
                }
            });
        },
        onCorpInput(value) {
            this.supplierForm.corp = value.join(",");
            console.log(this.supplierForm, value);
        },
        handleTaxInput(value) {
            if (!value) {
                this.supplierForm.supplier_tax = '';
                return;
            }

            // 只允许输入数字和小数点
            value = value.replace(/[^\d.]/g, '');
            
            // 确保只有一个小数点
            const parts = value.split('.');
            if (parts.length > 2) {
                value = parts[0] + '.' + parts[1];
            }

            // 限制小数位数为2位
            if (parts[1] && parts[1].length > 2) {
                value = parts[0] + '.' + parts[1].slice(0, 2);
            }

            // 确保值在0-0.99之间
            const num = parseFloat(value);
            if (!isNaN(num) && num >= 1) {
                value = '0.99';
            }

            this.supplierForm.supplier_tax = value;
        },
    },
};
</script>
<style>
.w-220 {
    width: 220px;
}
</style>

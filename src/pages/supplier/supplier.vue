<template>
    <div>
        <el-card shadow="hover">
            <el-input
                class="w-large m-r-10"
                size="mini"
                v-model="supplierListQuery.keyword"
                placeholder="请输入关键词"
                @keyup.enter.native="querySupplier"
                clearable
            ></el-input>
            <el-button type="warning" size="mini" @click="querySupplier"
                >查询</el-button
            >
            <el-button type="success" size="mini" @click="addSupplier"
                >添加</el-button
            >
        </el-card>
        <el-card shadow="hover">
            <el-table
                :data="supplierList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="供应商名称" prop="supplier_name">
                </el-table-column>
                <!--  corp 收款主体 -->

                <el-table-column label="供应商负责人" prop="supplier_director">
                </el-table-column>
                <el-table-column label="联系方式" prop="supplier_tel">
                </el-table-column>
                <el-table-column label="收款公司主体" prop="corp">
                    <template slot-scope="scope">
                        <div v-for="value in scope.row.corps" :key="value">
                            {{ value }}
                        </div>
                        <!-- <span v-if="scope.row.corp == '001'">佰酿云酒（重庆）科技有限公司</span>
                        <span v-else-if="scope.row.corp == '002'">重庆云酒佰酿电子商务有限公司</span>
                        <span v-else>其他</span> -->
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            type="primary"
                            @click="editSupplier(scope.row)"
                            >编辑</el-button
                        >
                        <el-button
                            size="mini"
                            type="danger"
                            @click="onDelete(scope.row)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <el-dialog
            title="供应商信息"
            :visible.sync="addSupplierVisible"
            width="40%"
            :close-on-click-modal="false"
        >
            <UpdateSupplier
                v-if="addSupplierVisible"
                ref="updateSupplier"
                @updateSupplier="updateSupplier"
            ></UpdateSupplier>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="addSupplierVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmUpdate"
                    >确定</el-button
                >
            </span>
        </el-dialog>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="supplierListQuery.page"
                :page-size="supplierListQuery.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
import UpdateSupplier from "./add.vue";
export default {
    components: { UpdateSupplier },
    data() {
        return {
            addSupplierVisible: false,
            supplierList: [],
            supplierListQuery: {
                keyword: "",
                page: 1,
                limit: 10,
            },
            total: 0,
        };
    },
    mounted() {
        this.getSupplierList();
    },
    methods: {
        querySupplier() {
            this.supplierListQuery.page = 1;
            this.getSupplierList();
        },
        updateSupplier() {
            this.$message({
                type: "success",
                message: "操作成功",
            });
            this.supplierListQuery.page = 1;
            this.addSupplierVisible = false;
            this.getSupplierList();
        },
        confirmUpdate() {
            this.$nextTick(() => {
                this.$refs.updateSupplier.submitSupplier();
            });
        },
        editSupplier(row) {
            this.addSupplierVisible = true;
            this.$nextTick(() => {
                this.$refs.updateSupplier.getEditDetails(row);
            });
        },
        onDelete(row) {
            this.$confirm(`确定删除供应商${row.supplier_name}吗?`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.$request.supplier
                    .deleteSupplier({ id: row.id })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("操作成功");
                            this.getSupplierList();
                        }
                    });
            });
        },
        addSupplier() {
            this.addSupplierVisible = true;
        },
        getSupplierList() {
            this.$request.supplier
                .getSupplierList({ ...this.supplierListQuery })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.supplierList = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        handleSizeChange(limit) {
            this.supplierListQuery.limit = limit;
            this.supplierListQuery.page = 1;
            this.getSupplierList();
        },
        handleCurrentChange(page) {
            this.supplierListQuery.page = page;
            this.getSupplierList();
        },
    },
};
</script>

<style lang="scss" scoped>
.el-card {
    margin-bottom: 10px;
}
.demo-table-expand {
    font-size: 0;
}
.demo-table-expand {
    /deep/.el-form-item__label {
        width: 100px;
        color: #99a9bf;
    }
}

.demo-table-expand {
    .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 50%;
    }
}

.filter {
    display: flex;
}
.table {
    margin-top: 10px;
}
</style>

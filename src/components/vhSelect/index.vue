<template>
    <div>
        <el-popover
            placement="bottom"
            width="300"
            trigger="manual"
            v-model="visible"
        >
            <div>
                <div v-if="loading" class="tips">加载中...</div>
                <div v-if="!options.length" class="tips">暂无数据</div>
                <div class="p-l-5 p-r-5 option-box">
                    <div
                        class="option-item"
                        :title="item"
                        v-for="(item, index) in options"
                        :key="index"
                        @click="choose(item)"
                    >
                        {{ item }}
                    </div>
                </div>
            </div>
            <!-- <div
                contenteditable
                :placeholder="label"
                class="input-box"
                slot="reference"
            >
                {{ name }}
            </div> -->
            <el-input
                slot="reference"
                v-model="name"
                :placeholder="label"
                size="mini"
                class="w-large m-r-10"
                @input="remoteMethod"
                @blur="inputBlur"
                @focus="inputFocus"
            ></el-input>
        </el-popover>
    </div>
</template>

<script>
import { selectType } from "@/tools/mapperModel.js";
export default {
    props: {
        type: {
            type: String,
            default: "",
        },
        label: {
            type: String,
            default: "",
        },
        productCategory: {
            type: Number,
            default: 1,
        },
    },
    data() {
        return {
            selectType,
            visible: false,
            loading: false,
            name: "",
            options: [],
        };
    },
    methods: {
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                let data = {};
                data[this.selectType[this.type]] = query;
                data.product_category = this.productCategory;
                this.$request.supplier.searchGetId(data).then((res) => {
                    if (res.data.error_code == 0) {
                        this.loading = false;
                        this.visible = true;
                        this.options = res.data.data?.list;
                    }
                });
            } else {
                this.options = [];
            }
        },
        choose(item) {
            this.name = "";
            this.name = item;
            this.visible = false;
        },
        inputBlur() {
            this.visible = false;
        },
        inputFocus() {
            this.visible = true;
        },
    },
};
</script>

<style lang="scss" scoped>
.input-box {
    width: 200px;
    height: 28px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    margin-right: 10px;
    padding-left: 10px;
    padding-top: 2px;
}
.option-box {
    max-height: 320px;
    overflow: auto;
}
.option-item {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    padding: 3px 2px;
}
.option-item:hover {
    background-color: #dcdfe6a0;
    cursor: pointer;
}
.tips {
    text-align: center;
    color: #919293;
}
</style>

<template>
    <ul class="nav">
        <li class="nav-profile">
            <img :src="userinfo.avatar" alt="" />
            <div class="info">
                {{ userinfo.realname }}
                <small>{{ userinfo.title }}</small>
            </div>
        </li>
        <div class="hr-line"></div>
    </ul>
</template>

<script>
import PageOptions from "../../config/PageOptions.vue";

export default {
    name: "SidebarNavProfile",
    data() {
        return {
            userinfo: {
                realname: "",
                avatar: "",
                title: "",
            },

            stat: "",
            pageOptions: PageOptions,
        };
    },
    mounted() {
        this.getUserInfo();
    },
    methods: {
        getUserInfo() {
            const userinfo = localStorage.getItem("userinfo");
            this.userinfo = JSON.parse(userinfo);
        },
        expand: function () {
            this.stat = this.stat == "expand" ? "collapse" : "expand";
        },
    },
};
</script>
<style lang="scss" scoped>
.nav-profile {
    text-align: center;
    background-color: transparent !important;
    img {
        width: 50px;
        margin-bottom: 10px;
        height: 50px;
        border-radius: 100%;
    }
}
.hr-line {
    background-color: rgba($color: #e8e8e8, $alpha: 0.2);
    width: 100%;
    height: 0.4px;
}
</style>

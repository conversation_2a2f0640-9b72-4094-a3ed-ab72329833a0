<template>
    <!-- begin sidebar nav -->
    <ul class="nav" v-if="menus">
        <li class="nav-search" v-if="pageOptions.pageSidebarSearch">
            <input
                class="form-control"
                placeholder="Sidebar menu filter..."
                type="text"
                v-on:keyup="handleSidebarFilter"
            />
        </li>
        <li class="nav-header">菜单导航</li>
        <template v-for="menu in menus">
            <sidebar-nav-list
                ref="sidebarNavList"
                v-bind:key="menu.path"
                v-bind:menu="menu"
                v-bind:scrollTop="scrollTop"
                v-bind:status="menu.status"
                v-on:collapse-other="handleCollapseOther(menu)"
                v-on:hideFloatSubmenu="handleHideFloatSubmenu"
                v-on:showFloatSubmenu="handleShowFloatSubmenu"
            ></sidebar-nav-list>
        </template>
        <!-- begin sidebar minify button -->
        <li>
            <a
                class="sidebar-minify-btn"
                href="javascript:;"
                v-on:click="handleSidebarMinify()"
                ><i class="fa fa-angle-double-left"></i
            ></a>
        </li>
        <!-- end sidebar minify button -->
    </ul>
    <!-- end sidebar nav -->
</template>

<script>
import SidebarNavList from "./SidebarNavList.vue";
import PageOptions from "../../config/PageOptions.vue";
import common from "../../services/common";
export default {
    name: "SidebarNav",
    props: ["scrollTop"],
    components: {
        SidebarNavList,
    },
    data() {
        return {
            menus: [],
            pageOptions: PageOptions,
        };
    },
    mounted() {
        this.getCatalogueList();
    },
    methods: {
        checkVersion(newVersion, oldVersion) {
            const path = "/" + window.location.pathname.split("/")[1];
            console.log(newVersion, oldVersion);
            if (!oldVersion) {
                window.localStorage.setItem(path, newVersion);
                return;
            }
            if (newVersion !== oldVersion) {
                this.$alert(
                    "当前系统已升级，请更新到最新版本 #" + newVersion,
                    "版本更新",
                    {
                        confirmButtonText: "更新版本",
                        showClose: false,
                        callback: (action) => {
                            console.log(action);
                            window.localStorage.setItem(path, newVersion);
                            window.location.reload();
                        },
                    }
                );
            }
        },
        async getCatalogueList() {
            const path = "/" + window.location.pathname.split("/")[1];
            const system_res = await common.getSystemsList();
            if (system_res.data.error_code == 0) {
                const systemList = system_res.data.data.list;
                let findModule = systemList.find((i) => i.path == path);
                if (findModule) {
                    let data = {
                        system_id: findModule.id,
                    };
                    console.log(findModule.version);
                    const oldVersion = window.localStorage.getItem(path);
                    this.checkVersion(findModule.version, oldVersion);
                    const res = await common.getCatalogueList(data);
                    if (res.data.error_code == 0) {
                        let routes = [];
                        res.data.data.list.forEach((item) => {
                            item["title"] = item["name"];
                            if (item.children.length == 0) {
                                let route = {
                                    path: item.router_path,
                                    meta: {
                                        title: item.name,
                                        operations: item.operations.map(
                                            ({ operate }) => operate
                                        ),
                                    },
                                    component: (resolve) =>
                                        require([
                                            `@/pages${item.router_file}`,
                                        ], resolve),
                                };
                                item["path"] = item["router_path"];
                                delete item["children"];
                                routes.push(route);
                            } else {
                                item.children.map((children) => {
                                    let route = {
                                        path: children.router_path,
                                        meta: {
                                            title: children.name,
                                            operations: children.operations.map(
                                                ({ operate }) => operate
                                            ),
                                        },
                                        component: (resolve) =>
                                            require([
                                                `@/pages${children.router_file}`,
                                            ], resolve),
                                    };

                                    children["path"] = children["router_path"];
                                    children["title"] = children["name"];
                                    delete children["children"];
                                    routes.push(route);
                                });
                            }
                        });
                        this.menus = res.data.data.list;
                        this.$router.addRoutes(routes);
                        this.$store.dispatch("setRoutesList", this.menus);
                    }
                }
            }
        },
        handleShowFloatSubmenu: function (menu, offset) {
            this.$emit("showFloatSubmenu", menu, offset);
        },
        handleHideFloatSubmenu: function () {
            this.$emit("hideFloatSubmenu");
        },
        handleCollapseOther: function (menu) {
            for (var i = 0; i < this.menus.length; i++) {
                this.$refs.sidebarNavList[i].collapse(menu);
            }
        },
        handleSidebarMinify: function () {
            this.pageOptions.pageSidebarMinified =
                !this.pageOptions.pageSidebarMinified;
        },
        handleSidebarFilter: function (e) {
            var value = e.target.value;
            value = value.toLowerCase();

            if (value) {
                for (var x = 0; x < this.menus.length; x++) {
                    var title = this.menus[x].title.toLowerCase();
                    var children = this.menus[x].children;

                    if (title.search(value) > -1) {
                        this.$refs.sidebarNavList[x].show();

                        if (children) {
                            this.$refs.sidebarNavList[x].searchExpand();
                        }
                    } else {
                        if (children) {
                            var hasActive = false;
                            for (var y = 0; y < children.length; y++) {
                                var title2 = children[y].title.toLowerCase();

                                if (title2.search(value) > -1) {
                                    hasActive = true;
                                    this.$refs.sidebarNavList[
                                        x
                                    ].$refs.sidebarNavList[y].show();
                                    this.$refs.sidebarNavList[x].searchExpand();
                                } else {
                                    if (hasActive) {
                                        this.$refs.sidebarNavList[
                                            x
                                        ].searchExpand();
                                    } else {
                                        this.$refs.sidebarNavList[x].hide();
                                    }
                                    this.$refs.sidebarNavList[
                                        x
                                    ].$refs.sidebarNavList[y].hide();
                                }
                            }
                        } else {
                            this.$refs.sidebarNavList[x].hide();
                        }
                    }
                }
            } else {
                for (var a = 0; a < this.menus.length; a++) {
                    this.$refs.sidebarNavList[a].show();

                    var submenu = this.menus[a].children;
                    if (submenu) {
                        for (var b = 0; b < submenu.length; b++) {
                            this.$refs.sidebarNavList[a].$refs.sidebarNavList[
                                b
                            ].show();
                        }
                    }
                }
            }
            console.log("------");
        },
    },
};
</script>

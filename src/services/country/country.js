import axios from "axios";

function getCountryList(data) {
    // 获取guojia列表
    return axios({
        url: "/api/wiki/v3/country",
        method: "get",
        params: data,
    });
}

function getContparam(data) {
    return axios({
        url: "/api/wiki/v3/country/contparam",
        method: "get",
        params: data,
    });
}
function addCountry(data) {
    return axios({
        url: "/api/wiki/v3/country",
        method: "post",
        data,
    });
}
function updateCountry(data) {
    return axios({
        url: "/api/wiki/v3/country/update",
        method: "post",
        data,
    });
}
function deleteCountry(data) {
    return axios({
        url: "/api/wiki/v3/country/delete",
        method: "post",
        data,
    });
}
function getWineList(data) {
    return axios({
        url: "/api/wiki/v3/wine",
        method: "get",
        params: data,
    });
}
function getCountryDetails(data) {
    return axios({
        url: "/api/wiki/v3/country/info",
        method: "get",
        params: data,
    });
}
function addHistory(data) {
    return axios({
        url: "/api/wiki/v3/country/his",
        method: "post",
        data,
    });
}
function deleteHistory(data) {
    return axios({
        url: "/api/wiki/v3/country/his/delete",
        method: "post",
        data,
    });
}
function updateHistory(data) {
    return axios({
        url: "/api/wiki/v3/country/his/update",
        method: "post",
        data,
    });
}
export default {
    getCountryList,
    getContparam,
    addCountry,
    deleteCountry,
    getWineList,
    updateCountry,
    getCountryDetails,
    addHistory,
    deleteHistory,
    updateHistory,
};

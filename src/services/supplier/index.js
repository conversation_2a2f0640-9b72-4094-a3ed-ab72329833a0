import axios from "axios";

function getSupplierList(data) {
    // 获取供应商列表
    return axios({
        url: "/api/wiki/v3/supplier",
        method: "get",
        params: data,
    });
}

function addSupplier(data) {
    // 添加供应商
    return axios({
        url: "/api/wiki/v3/supplier",
        method: "post",
        data,
    });
}
function updateSupplier(data) {
    // 添加供应商
    return axios({
        url: "/api/wiki/v3/supplier/update",
        method: "post",
        data,
    });
}
function deleteSupplier(data) {
    return axios({
        url: "/api/wiki/v3/supplier/delete",
        method: "post",
        data,
    });
}
//供应商产品类别
function productCategory() {
    return axios({
        url: "/api/supplier-product/v3/supplierproduct/productCategory",
        method: "get",
    });
}
//酒类批量导入
// function supplierproductImpport(data) {
//     return axios({
//         url: "/api/supplier-product/v3/supplierproduct/import",
//         method: "post",
//         data,
//     });
// }
function supplierproductImpport(data) {
    return axios({
        url: "/api/supplier-product/v3/import/batchImport",
        method: "post",
        data,
    });
}
//供应商列表
function supplierList(data) {
    return axios({
        url: "/api/supplychain/v3/partnerentity/list",
        method: "get",
        params: data,
    });
}
//供应商商品导入列表
function supplierproductList(data) {
    return axios({
        url: "/api/supplier-product/v3/supplierproduct/lists",
        method: "get",
        params: data,
    });
}

//开售
function onSale(data) {
    return axios({
        url: "/api/commodities/v3/periodsPool/onSale",
        method: "post",
        data,
    });
}
// 最后售卖时间
function batchLastSellTime(params) {
    return axios({
        url: "/api/commodities/v3/period/batchLastSellTime",
        method: "get",
        params,
    });
}

//获取磐石产品数据
function getProducts(data) {
    return axios({
        url: "/api/supplier-product/v3/supplierproduct/getProducts",
        method: "get",
        params: data,
    });
}
//简码绑定
function relatedShortCode(data) {
    return axios({
        url: "/api/supplier-product/v3/supplierproduct/relatedShortCode",
        method: "post",
        data,
    });
}
//商品详情
function goodsDetail(data) {
    return axios({
        url: "/api/supplier-product/v3/supplierproduct/detail",
        method: "get",
        params: data,
    });
}
//商品详情修改
function goodsDetailUpdate(data) {
    return axios({
        url: "/api/supplier-product/v3/supplierproduct/update",
        method: "post",
        data,
    });
}
//获取产品详情
function productDetail(data) {
    return axios({
        url: "/api/wiki/v3/product/info",
        method: "get",
        params: data,
    });
}
//添加产品
function addProduct(data) {
    return axios({
        url: "/api/supplier-product/v3/supplierproduct/add",
        method: "post",
        data,
    });
}
//商品名产区搜索
function searchGetId(data) {
    return axios({
        url: "/api/supplier-product/v3/supplierproduct/searchGetId",
        method: "get",
        params: data,
    });
}
// 所有供应商列表
function allListSupplier(data) {
    return axios({
        url: "/api/supplychain/v3/partnerentity/alllist",
        method: "get",
        params: data,
    });
}
export default {
    getSupplierList,
    addSupplier,
    updateSupplier,
    deleteSupplier,
    productCategory,
    supplierproductImpport,
    supplierList,
    supplierproductList,
    onSale,
    batchLastSellTime,
    getProducts,
    relatedShortCode,
    goodsDetail,
    goodsDetailUpdate,
    productDetail,
    addProduct,
    searchGetId,
    allListSupplier,
};

import axios from "axios";

function getWineryList(data) {
    // 产区列表
    return axios({
        url: "/api/wiki/v3/winery",
        method: "get",
        params: data,
    });
}
//获取酒庄等级
function getWineryGrade(data) {
    return axios({
        url: "/api/wiki/v3/winerygrade",
        method: "get",
        params: data,
    });
}
//添加产区
function addWinery(data) {
    return axios({
        url: "/api/wiki/v3/winery",
        method: "post",
        data,
    });
}
//更新产区
function updateWinery(data) {
    return axios({
        url: "/api/wiki/v3/winery/update",
        method: "post",
        data,
    });
}
//删除产区
function deleteWinery(data) {
    return axios({
        url: "/api/wiki/v3/winery/delete",
        method: "post",
        data,
    });
}
//获取产区详情
function getWineryDetails(data) {
    return axios({
        url: "/api/wiki/v3/winery/info",
        method: "get",
        params: data,
    });
}
//获取酒款列表
function getWineList(data) {
    return axios({
        url: "/api/wiki/v3/wine",
        method: "get",
        params: data,
    });
}
//添加产区历史
function addWineryHistory(data) {
    return axios({
        url: "/api/wiki/v3/winery/his",
        method: "post",
        data,
    });
}
function deleteWineryHistory(data) {
    return axios({
        url: "/api/wiki/v3/winery/his/delete",
        method: "post",
        data,
    });
}
function updateWineryHistory(data) {
    return axios({
        url: "/api/wiki/v3/winery/his/update",
        method: "post",
        data,
    });
}
function getWineryClassify(data) {
    return axios({
        url: "/api/wiki/v3/wineryclassify",
        method: "get",
        params: data,
    });
}
function addWineryHonor(data){
    return axios({
        url: "/api/wiki/v3/winery/hon",
        method: "post",
        data,
    });
}
function deleteWineryHonor(data){
    return axios({
        url: "/api/wiki/v3/winery/hon/delete",
        method: "post",
        data,
    });
} 
function updateWineryHonor(data){
    return axios({
        url: "/api/wiki/v3/winery/hon/update",
        method: "post",
        data,
    });
}
///wiki/v3/winery/annex
function addWineryAnnex(data){
    return axios({
        url: "/api/wiki/v3/winery/annex",
        method: "post",
        data,
    });
}
///wiki/v3/winery/annex/delete
function deleteWineryAnnex(data){
    return axios({
        url: "/api/wiki/v3/winery/annex/delete",
        method: "post",
        data,
    });
}

export default {
    getWineryList,
    getWineryGrade,
    addWinery,
    deleteWinery,
    getWineList,
    updateWinery,
    getWineryDetails,
    addWineryHistory,
    deleteWineryHistory,
    updateWineryHistory,
    getWineryClassify,
    addWineryHonor,
    deleteWineryHonor,
    updateWineryHonor,
    addWineryAnnex,
    deleteWineryAnnex
};

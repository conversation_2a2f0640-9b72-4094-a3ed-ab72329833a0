import axios from "axios";

function getRegionList(data) {
    // 产区列表
    return axios({
        url: "/api/wiki/v3/regions",
        method: "get",
        params: data,
    });
}
//产区参数
function getContparam(data) {
    return axios({
        url: "/api/wiki/v3/regions/contparam",
        method: "get",
        params: data,
    });
}
//添加产区
function addRegion(data) {
    return axios({
        url: "/api/wiki/v3/regions",
        method: "post",
        data,
    });
}
//更新产区
function updateRegion(data) {
    return axios({
        url: "/api/wiki/v3/regions/update",
        method: "post",
        data,
    });
}
//删除产区
function deleteRegion(data) {
    return axios({
        url: "/api/wiki/v3/regions/delete",
        method: "post",
        data,
    });
}
//获取产区详情
function getRegionDetails(data) {
    return axios({
        url: "/api/wiki/v3/regions/info",
        method: "get",
        params: data,
    });
}
//获取酒款列表
function getWineList(data) {
    return axios({
        url: "/api/wiki/v3/wine",
        method: "get",
        params: data,
    });
}
//添加产区历史
function addRegionHistory(data) {
    return axios({
        url: "/api/wiki/v3/regions/his",
        method: "post",
        data,
    });
}
function deleteRegionHistory(data) {
    return axios({
        url: "/api/wiki/v3/regions/his/delete",
        method: "post",
        data,
    });
}
function updateRegionHistory(data) {
    return axios({
        url: "/api/wiki/v3/regions/his/update",
        method: "post",
        data,
    });
}
///wiki/v3/regions/map
function  getRegionMap(data) {
    return axios({
        url: "/api/wiki/v3/regions/map",
        method: "get",
        params: data,
    });
}
export default {
    getRegionList,
    getContparam,
    addRegion,
    deleteRegion,
    getWineList,
    updateRegion,
    getRegionDetails,
    addRegionHistory,
    deleteRegionHistory,
    updateRegionHistory,
    getRegionMap
};
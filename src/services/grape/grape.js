import axios from "axios";

function getGrapeList(data) {
    // 获取葡萄列表
    return axios({
        url: "/api/wiki/v3/grape",
        method: "get",
        params: data,
    });
}

function getContparam(data) {
    return axios({
        url: "/api/wiki/v3/grapeparam",
        method: "get",
        params: data,
    });
}
function addGrape(data) {
    return axios({
        url: "/api/wiki/v3/grape",
        method: "post",
        data,
    });
}
function updateGrape(data) {
    return axios({
        url: "/api/wiki/v3/grape/update",
        method: "post",
        data,
    });
}
function deleteGrape(data) {
    return axios({
        url: "/api/wiki/v3/grape/delete",
        method: "post",
        data,
    });
}
function getWineList(data) {
    return axios({
        url: "/api/wiki/v3/wine",
        method: "get",
        params: data,
    });
}
function getGrapeDetails(data) {
    return axios({
        url: "/api/wiki/v3/grape/info",
        method: "get",
        params: data,
    });
}
function addHistory(data) {
    return axios({
        url: "/api/wiki/v3/grape/his",
        method: "post",
        data,
    });
}
function deleteHistory(data) {
    return axios({
        url: "/api/wiki/v3/grape/his/delete",
        method: "post",
        data,
    });
}
function updateHistory(data) {
    return axios({
        url: "/api/wiki/v3/grape/his/update",
        method: "post",
        data,
    });
}
export default {
    getGrapeList,
    getContparam,
    addGrape,
    deleteGrape,
    getWineList,
    updateGrape,
    getGrapeDetails,
    addHistory,
    deleteHistory,
    updateHistory,
};

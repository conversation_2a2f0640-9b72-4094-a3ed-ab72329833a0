import axios from "axios";

function getGoodstypeSelect(data) {
    //产品类别(大类)列表
    return axios({
        // url: "/api/databank/v3/products/category",old
        url: "/api/wiki/v3/productcategorys",
        method: "get",
        data,
    });
}
//根据id获取类型
function getPropertyList(params) {
    return axios({
        url: "/api/wiki/v3/productcategory/property",
        // url:"/api/databank/v3/products/getProperty",old
        method: "get",
        params,
    });
}
function createProduct(data) {
    return axios({
        url: "/api/wiki/v3/product",
        method: "post",
        data,
    });
}
function updateProduct(data) {
    return axios({
        url: "/api/wiki/v3/product/update",
        method: "post",
        data,
    });
}
function getProductList(params) {
    return axios({
        url: "/api/wiki/v3/product",
        method: "get",
        params,
    });
}
function getProductDetails(params) {
    return axios({
        url: "/api/wiki/v3/product/info",
        method: "get",
        params,
    });
}
function getBarCode(params) {
    return axios({
        url: "/api/wiki/v3/product/getBarCode",
        method: "post",
        data: params,
    });
}
function getCountryList(params) {
    return axios({
        url: "/api/wiki/v3/country",
        method: "get",
        params,
    });
}
//产区
function getRegionList(params) {
    return axios({
        url: "/api/wiki/v3/regions",
        method: "get",
        params,
    });
}
//酒庄
function getWineryList(params) {
    return axios({
        url: "/api/wiki/v3/winery",
        method: "get",
        params,
    });
}
function getGrapeList(params) {
    return axios({
        url: "/api/wiki/v3/grape",
        method: "get",
        params,
    });
}
//获取仓库
function getStoreList(params) {
    return axios({
        url: "/api/wiki/v3/product/extend",
        method: "get",
        params,
    });
}
function getProductCategory(params) {
    return axios({
        url: "/api/wiki/v3/productcategory/type",
        method: "get",
        params,
    });
}
function getKeyword(params) {
    return axios({
        url: "/api/wiki/v3/product/keywords",
        method: "get",
        params,
    });
}
function addKeyword(params) {
    return axios({
        url: "/api/wiki/v3/product/keywords",
        method: "post",
        data: params,
    });
}
function syncwms(params) {
    return axios({
        url: "/api/wiki/v3/product/syncwms",
        method: "post",
        data: params,
    });
}
function getMerchantList(params) {
    return axios({
        url: "/api/vmall/v3/merchant/list",
        method: "get",
        params,
    });
}
function exceldatacreate(params) {
    return axios({
        url: "/api/wiki/v3/product/exceldatacreate",
        method: "get",
        params,
    });
}
// /wiki/v3/product/retrycreate

function retrycreate(params) {
    return axios({
        url: "/api/wiki/v3/product/retrycreate",
        method: "post",
        data: params,
    });
}
// /erp/v3/erp/taxItems
function getTaxItems(params) {
    return axios({
        url: "/api/erp/v3/erp/taxItems",
        method: "get",
        params,
    });
}
//批量上传关单附件
function uploadBatchUpdateAttachment(params) {
    return axios({
        url: "/api/wiki/v3/product/batchUpdateAttachment",
        method: "post",
        data: params,
    });
}
//修改发票名称
function updateInvoiceName(params) {
    return axios({
        url: "/api/wiki/v3/product/updateInvoiceName",
        method: "post",
        data: params,
    });
}
export default {
    getGoodstypeSelect,
    getPropertyList,
    getProductList,
    createProduct,
    updateProduct,
    getProductDetails,
    getBarCode,
    getCountryList,
    getRegionList,
    getWineryList,
    getGrapeList,
    getStoreList,
    getProductCategory,
    getKeyword,
    syncwms,
    addKeyword,
    getMerchantList,
    exceldatacreate,
    retrycreate,
    getTaxItems,
    uploadBatchUpdateAttachment,
    updateInvoiceName,
};

import axios from "axios";
//获取酒类参数
function getAscategorylist(data) {
    return axios({
        url: "/api/wiki/v3/wineparam",
        method: "get",
        params: data,
    });
}
//获取酒列表
function getWineList(data) {
    return axios({
        url: "/api/wiki/v3/wine",
        method: "get",
        params: data,
    });
}
//
function getGrapeList(data) {
    return axios({
        url: "/api/wiki/v3/grape",
        method: "get",
        params: data,
    });
}

// 获取酒详情
function getWineDetails(data) {
    return axios({
        url: "/api/wiki/v3/wine/info",
        method: "get",
        params: data,
    });
}
//更新酒类
function updateWinestyle(data) {
    return axios({
        url: "/api/wiki/v3/wine/update",
        method: "post",
        data,
    });
}
//添加酒类
function addWinestyle(data) {
    return axios({
        url: "/api/wiki/v3/wine",
        method: "post",
        data,
    });
}
function getWineComment(data) {
    return axios({
        url: "/api/wiki/v3/winecomment",
        method: "get",
        params: data,
    });
}
function createWineComment(data) {
    return axios({
        url: "/api/wiki/v3/winecomment/create",
        method: "post",
        data,
    });
}
function getVestList(data) {
    // 我的马甲列表
    return axios({
        url: "/api/user/v3/vestuser/get",
        method: "get",
        params: data,
    });
}
function getSkepticalWine(data) {
    // 获取纠错列表
    return axios({
        url: "/api/wiki/v3/wineec/index",
        method: "get",
        params: data,
    });
}
function getSkepticalWineDetails(data) {
    // 获取纠错列表
    return axios({
        url: "/api/wiki/v3/wineec/detail",
        method: "get",
        params: data,
    });
}
function changeWineDetailsStauts(data) {
    //
    return axios({
        url: "/api/wiki/v3/wineec/checkwinc",
        method: "post",
        data,
    });
}
function changeWineCommentStatus(data) {
    //
    return axios({
        url: "/api/wiki/v3/winecomment/status",
        method: "post",
        data,
    });
}
function getExtraWine(data) {
    // 获取补充列表
    return axios({
        url: "/api/wiki/v3/winecsupplement/index",
        method: "get",
        params: data,
    });
}
function getExtraDetails(data) {
    // 获取补充详情
    return axios({
        url: "/api/wiki/v3/winecsupplement/detail",
        method: "get",
        params: data,
    });
}
function changeExtraWineStatus(data) {
    //酒款核对发放兔头
    return axios({
        url: "/api/wiki/v3/winecsupplement/checkwinc",
        method: "post",
        data,
    });
}

export default {
    getAscategorylist,
    getWineList,
    getWineDetails,
    addWinestyle,
    updateWinestyle,
    getWineComment,
    createWineComment,
    getVestList,
    getSkepticalWine,
    getSkepticalWineDetails,
    changeWineDetailsStauts,
    changeWineCommentStatus,
    getExtraWine,
    getExtraDetails,
    changeExtraWineStatus,
    getGrapeList
};
